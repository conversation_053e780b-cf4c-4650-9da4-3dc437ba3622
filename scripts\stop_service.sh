#!/bin/bash

# 串口通信服务停止脚本
# 功能：停止串口通信服务并检查状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    printf "${GREEN}[INFO]${NC} %s\n" "$1"
}

log_warn() {
    printf "${YELLOW}[WARN]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 服务名称
SERVICE_NAME="serialport-service"

# 检查是否以root权限运行
if [ "$(id -u)" -ne 0 ]; then
   log_error "此脚本需要root权限运行"
   exit 1
fi

# 检查服务是否存在
if ! systemctl list-unit-files | grep -q "$SERVICE_NAME.service"; then
    log_warn "服务 $SERVICE_NAME 不存在"
    exit 0
fi

# 检查服务当前状态
if systemctl is-active --quiet "$SERVICE_NAME"; then
    log_info "正在停止串口通信服务..."
    systemctl stop "$SERVICE_NAME"
    
    # 等待服务完全停止
    sleep 2
    
    # 验证服务已停止
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_error "服务停止失败！"
        systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    else
        log_info "服务已成功停止"
    fi
else
    log_info "服务已经处于停止状态"
fi

# 可选：禁用开机自启动
printf "是否禁用开机自启动？(y/N): "
read REPLY
case "$REPLY" in
    [Yy]* )
        log_info "禁用开机自启动..."
        systemctl disable "$SERVICE_NAME"
        log_info "已禁用开机自启动"
        ;;
    * )
        ;;
esac

# 显示最终状态
log_info "服务最终状态："
systemctl status "$SERVICE_NAME" --no-pager -l || true

log_info "服务管理命令："
echo "  启动服务: systemctl start $SERVICE_NAME"
echo "  停止服务: systemctl stop $SERVICE_NAME"
echo "  重启服务: systemctl restart $SERVICE_NAME"
echo "  查看状态: systemctl status $SERVICE_NAME"
echo "  查看日志: journalctl -u $SERVICE_NAME -f"