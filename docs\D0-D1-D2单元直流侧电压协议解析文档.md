# D0-D1-D2单元直流侧电压协议解析文档

## 1. 协议概述

本文档详细描述了D0、D1、D2三个协议的数据格式和解析方法，用于查询A、B、C三相单元的直流侧电压数据。这三个协议分别对应三相单元的电压查询，协议结构相似但命令字节不同。

## 2. 协议基本格式

### 2.1 协议结构

所有协议遵循统一的数据帧格式：

| 字段 | 长度 | 说明 |
|------|------|------|
| 帧头 | 2字节 | 固定为 `EB 90` |
| 地址 | 1字节 | 固定为 `01` |
| 功能码 | 1字节 | 固定为 `01` |
| 命令码 | 1字节 | D0/D1/D2 |
| 数据长度 | 1字节 | 固定为 `00` |
| 数据区 | 8字节 | 固定为 `00 00 00 00 00 00 00 00` |
| 校验码 | 1字节 | 根据命令计算 |
| 帧尾 | 2字节 | 固定为 `AA AB` |

## 3. 协议详细说明

### 3.1 D0协议 - A相单元直流侧电压查询

#### 3.1.1 发送命令
```
EB 90 01 01 D0 00 00 00 00 00 00 00 00 4D AA AB
```

#### 3.1.2 接收响应格式
```
EB 90 01 01 D0 24 00 00 00 [36字节电压数据] [2字节校验] AA AB
```

#### 3.1.3 响应示例
```
EB 90 01 01 D0 24 00 00 00 45 02 45 02 44 02 43 02 44 02 44 02 45 02 44 02 45 02 45 02 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 39 AA AB
```

### 3.2 D1协议 - B相单元直流侧电压查询

#### 3.2.1 发送命令
```
EB 90 01 01 D1 00 00 00 00 00 00 00 00 4E AA AB
```

#### 3.2.2 接收响应格式
```
EB 90 01 01 D1 24 00 00 00 [36字节电压数据] [2字节校验] AA AB
```

#### 3.2.3 响应示例
```
EB 90 01 01 D1 24 00 00 00 44 02 44 02 44 02 44 02 44 02 44 02 44 02 45 02 45 02 43 02 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 37 AA AB
```

### 3.3 D2协议 - C相单元直流侧电压查询

#### 3.3.1 发送命令
```
EB 90 01 01 D2 00 00 00 00 00 00 00 00 4F AA AB
```

#### 3.3.2 接收响应格式
```
EB 90 01 01 D2 24 00 00 00 [36字节电压数据] [2字节校验] AA AB
```

#### 3.3.3 响应示例
```
EB 90 01 01 D2 24 00 00 00 47 02 47 02 4A 02 49 02 49 02 48 02 47 02 48 02 47 02 48 02 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 5F AA AB
```

## 4. 数据解析方法

### 4.1 电压数据格式

响应数据中的电压数据区域为36字节，每2字节表示一个单元的电压值，共18个单元位置（实际有效为12个单元）。

#### 4.1.1 数据排列
| 字节位置 | 对应单元 | 数据格式 |
|----------|----------|----------|
| 0-1 | 单元1 | 小端序16位无符号整数 |
| 2-3 | 单元2 | 小端序16位无符号整数 |
| 4-5 | 单元3 | 小端序16位无符号整数 |
| ... | ... | ... |
| 22-23 | 单元12 | 小端序16位无符号整数 |
| 24-35 | 单元13-18 | 保留区域（通常为0） |

#### 4.1.2 电压值计算
```
电压值 = 低字节 + 高字节 × 256
```

例如：数据 `02 45` 表示：
- 低字节：0x02
- 高字节：0x45
- 电压值 = 2 + 69 × 256 = 581

### 4.2 有效单元识别

- **有效单元**：1-10单元，对应字节位置0-19
- **旁路单元**：11-12单元，对应字节位置20-23，数据通常为`00 00`
- **保留区域**：13-18单元，对应字节位置24-35，数据通常为`00 00`

## 5. 解析代码示例

### 5.1 Python解析示例

```python
def parse_voltage_response(hex_string):
    """
    解析D0/D1/D2协议的电压响应数据
    
    Args:
        hex_string: 十六进制字符串格式的响应数据
    
    Returns:
        dict: 包含解析结果的字典
    """
    # 移除空格并转换为字节数组
    data = bytes.fromhex(hex_string.replace(" ", ""))
    
    # 验证帧头帧尾
    if data[0:2] != b'\xEB\x90' or data[-2:] != b'\xAA\xAB':
        raise ValueError("无效的帧格式")
    
    # 获取命令码
    command = data[3]
    phase_map = {0xD0: 'A', 0xD1: 'B', 0xD2: 'C'}
    phase = phase_map.get(command, '未知')
    
    # 提取电压数据（从第9字节开始，共36字节）
    voltage_data = data[9:45]
    
    # 解析每个单元的电压
    voltages = []
    for i in range(12):  # 只解析前12个有效单元
        pos = i * 2
        if pos + 1 < len(voltage_data):
            # 小端序转换
            value = voltage_data[pos] + (voltage_data[pos + 1] << 8)
            voltage = value  # 直接作为电压值，无需转换
            
            # 检查是否为旁路状态
            status = "旁路" if value == 0 and i >= 10 else "正常"
            
            voltages.append({
                'unit': i + 1,
                'voltage': voltage,
                'status': status,
                'raw_value': value
            })
    
    return {
        'phase': phase,
        'command': f"0x{command:02X}",
        'voltages': voltages,
        'raw_data': hex_string
    }

# 使用示例
if __name__ == "__main__":
    # D0协议响应示例
    d0_response = "EB 90 01 01 D0 24 00 00 00 45 02 45 02 44 02 43 02 44 02 44 02 45 02 44 02 45 02 45 02 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 39 AA AB"
    result = parse_voltage_response(d0_response)
    
    print(f"相位: {result['phase']}相")
    for v in result['voltages']:
        print(f"单元{v['unit']}: {v['voltage']:.1f}V ({v['status']})")
```

### 5.2 校验码计算方法

```python
def calculate_checksum(command_byte):
    """
    计算D0/D1/D2协议的校验码
    
    Args:
        command_byte: 命令字节 (0xD0, 0xD1, 0xD2)
    
    Returns:
        int: 校验码值
    """
    # 固定前缀
    prefix = [0xEB, 0x90, 0x01, 0x01, command_byte, 0x00, 0x00, 0x00, 
              0x00, 0x00, 0x00, 0x00, 0x00]
    
    # 计算校验和（简单的累加和）
    checksum = sum(prefix) & 0xFF
    return checksum

# 验证校验码
print(f"D0校验码: 0x{calculate_checksum(0xD0):02X}")  # 输出: 0x4D
print(f"D1校验码: 0x{calculate_checksum(0xD1):02X}")  # 输出: 0x4E
print(f"D2校验码: 0x{calculate_checksum(0xD2):02X}")  # 输出: 0x4F
```

## 6. 注意事项

### 6.1 数据有效性检查

1. **帧格式验证**：必须验证帧头`EB 90`和帧尾`AA AB`
2. **命令码验证**：确认响应的命令码与请求一致
3. **数据长度验证**：确认数据长度为36字节（电压数据区域）
4. **旁路状态识别**：11-12单元数据为`00 00`表示旁路状态

### 6.2 通信异常处理

1. **超时处理**：设置合理的超时时间（建议3-5秒）
2. **重试机制**：通信失败时进行2-3次重试
3. **错误日志**：记录通信异常和解析错误
4. **数据校验**：对解析结果进行合理性检查

### 6.3 电压值范围

- **正常范围**：大于0的任意正整数（根据实际设备规格，原始数据值）
- **异常值处理**：负数值应标记为异常，非0非负值均视为正常
- **零值处理**：电压为0时应检查是否为旁路状态

## 7. 常见问题解答

### Q1: 为什么11-12单元的数据总是0？
A: 当前系统配置中，11-12单元处于旁路状态，因此数据为0。

### Q2: 如何判断通信是否正常？
A: 检查响应帧的完整性、命令码一致性以及校验码的正确性。

### Q3: 电压数据的表示方式是什么？
A: 电压数据为原始数值，直接从小端序16位无符号整数获取，无需转换。

### Q4: 如何处理通信超时？
A: 建议设置3-5秒的超时时间，超时后进行2-3次重试，仍失败则记录错误日志。

## 8. 版本历史

| 版本 | 日期 | 修改内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2024-12-19 | 初始版本创建 | 系统管理员 |

---

**文档说明**：本文档基于实际测试数据编写，如有协议变更请及时更新文档内容。