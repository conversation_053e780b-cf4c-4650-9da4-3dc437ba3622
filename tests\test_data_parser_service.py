#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析器服务测试脚本
用于验证数据解析器服务的基本功能
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from src.services.data_parser_service import DataParserService


async def test_service():
    """测试数据解析器服务"""
    print("开始测试数据解析器服务...")
    
    # 创建测试数据目录
    test_data_dir = Path("test_data")
    test_data_dir.mkdir(exist_ok=True)
    
    # 创建服务实例
    service = DataParserService(str(test_data_dir))
    
    try:
        # 测试解析器脚本是否存在
        print("\n1. 检查解析器脚本...")
        parsers = {
            'c0': service.c0_parser,
            'c5': service.c5_parser,
            'c59': service.c59_parser
        }
        
        for name, parser_path in parsers.items():
            if parser_path.exists():
                print(f"✓ {name}解析器脚本存在: {parser_path}")
            else:
                print(f"✗ {name}解析器脚本不存在: {parser_path}")
        
        # 测试创建测试数据文件
        print("\n2. 创建测试数据文件...")
        
        # 创建测试的realtime_data.json
        realtime_test_data = [
            {
                "command": "C0",
                "timestamp": time.time(),
                "success": True,
                "data_hex_formatted": ["AA", "BB", "CC"]
            }
        ]
        
        realtime_file = test_data_dir / "realtime_data.json"
        with open(realtime_file, 'w', encoding='utf-8') as f:
            json.dump(realtime_test_data, f, indent=2)
        print(f"✓ 创建测试文件: {realtime_file}")
        
        # 创建测试的59_data.json
        data_59_test = [
            {
                "command": "59",
                "timestamp": time.time(),
                "success": True,
                "data_hex_formatted": ["59", "12345678"]
            }
        ]
        
        data_59_file = test_data_dir / "59_data.json"
        with open(data_59_file, 'w', encoding='utf-8') as f:
            json.dump(data_59_test, f, indent=2)
        print(f"✓ 创建测试文件: {data_59_file}")
        
        # 测试初始解析
        print("\n3. 测试初始解析功能...")
        await service.initial_parse()
        
        # 检查解析器运行状态
        print("\n4. 检查解析器运行状态...")
        for parser_name, is_running in service.parser_running.items():
            status = "运行中" if is_running else "空闲"
            print(f"  {parser_name}解析器: {status}")
        
        print("\n✓ 数据解析器服务测试完成")
        
    except Exception as e:
        print(f"\n✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        print("\n5. 清理测试文件...")
        try:
            if realtime_file.exists():
                realtime_file.unlink()
                print(f"✓ 删除: {realtime_file}")
            if data_59_file.exists():
                data_59_file.unlink()
                print(f"✓ 删除: {data_59_file}")
            if test_data_dir.exists() and not any(test_data_dir.iterdir()):
                test_data_dir.rmdir()
                print(f"✓ 删除测试目录: {test_data_dir}")
        except Exception as e:
            print(f"清理文件时出错: {e}")


def main():
    """主函数"""
    print("数据解析器服务测试工具")
    print("=" * 50)
    
    try:
        asyncio.run(test_service())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()