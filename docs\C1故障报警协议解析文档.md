# C1故障报警协议解析文档

## 协议规范

### 基本信息
- **命令码**：C1（故障报警）
- **功能**：读取系统故障报警状态
- **数据类型**：位状态数据（每位表示一个故障点的状态）

### 通信格式

#### 发送格式
```
EB 90 01 01 C1 00 00 00 00 00 00 00 00 3E AA AB
```

#### 接收格式
```
EB 90 01 01 C1 44 00 00 00 [数据34*16bit] [校验位] AA AB
```

### 数据结构
- **数据长度**：34个16位数据（68字节）
- **数据解析**：每个16位数据对应一个故障点表项，按位解析故障状态
- **字节序**：小端序（低字节在前）
- **位序**：LSB优先（最低位为第0位）

## 解析示例

### 示例报文
```
EB 90 01 01 C1 44 00 00 00 00 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 88 AA AB
```

### 解析过程
1. **提取数据段**：从第9字节开始的68字节数据
2. **按16位分组**：每2字节组成一个16位数据
3. **位状态解析**：
   - `02 00` → 0x0002 → 二进制：0000 0000 0000 0010 → 第1位置1
   - `04 00` → 0x0004 → 二进制：0000 0000 0000 0100 → 第2位置1

### 解析结果
- **24_9**：备用（对应第1个16位数据的第9位）- 实际为备用位，无具体故障含义
- **39_10**：合高压等待（对应第16个16位数据的第10位）- 系统状态指示

## 故障点表映射

### 映射规则
- **点表编号格式**：`起始点_位偏移`
- **起始点范围**：24-56（共33个起始点）
- **位偏移范围**：0-15（每个起始点16位）
- **总故障点数**：33 × 16 = 528个故障点

### 详细映射表

#### 第1组：24_0 到 24_15（第1个16位数据）
| 位偏移 | 点表编号 | 故障描述 |
|--------|----------|----------|
| 0 | 24_0 | 电网电压有效值Ⅰ段过压报警 |
| 1 | 24_1 | 电网电压有效值Ⅱ段过压保护 |
| 2 | 24_2 | 电网电压幅值Ⅲ段过压保护 |
| 3 | 24_3 | 电网电压瞬时值过压保护 |
| 4 | 24_4 | 电网电压有效值Ⅰ段欠压报警 |
| 5 | 24_5 | 电网电压有效值Ⅱ段欠压保护 |
| 6 | 24_6 | 电网电压幅值Ⅲ段欠压保护 |
| 7 | 24_7 | 电网电压有效值不平衡保护 |
| 8 | 24_8 | 电网电压缺相保护 |
| 9 | 24_9 | 备用 |
| 10 | 24_10 | 同步信号丢失保护 |
| 11 | 24_11 | SVG侧霍尔电流传感器故障 |
| 12 | 24_12 | 模拟板采样通道自检故障 |
| 13 | 24_13 | 零序电压超标保护 |
| 14 | 24_14 | SVG输出电流有效值Ⅰ段过流报警 |
| 15 | 24_15 | SVG输出电流有效值Ⅱ段过流保护 |

#### 第2组：25_0 到 25_15（第2个16位数据）
| 位偏移 | 点表编号 | 故障描述 |
|--------|----------|----------|
| 0 | 25_0 | SVG输出电流瞬时值过流1(Vpp) |
| 1 | 25_1 | SVG输出电流硬件过流(HW) |
| 2 | 25_2 | SVG输出电流缺相保护 |
| 3 | 25_3 | 输出电流指令限幅 |
| 4 | 25_4 | SVG瞬时电流过流（CT检测方式）故障 |
| 5 | 25_5 | PT故障 |
| 6 | 25_6 | SVG零序电流故障 |
| 7 | 25_7 | 功率单元状态一般性故障(SW) |
| 8 | 25_8 | 功率单元UDC过压保护(SW) |
| 9 | 25_9 | 功率单元UDC不平衡保护(SW) |
| 10 | 25_10 | 功率单元硬件保护(HW) |
| 11 | 25_11 | 单元自检故障 |
| 12 | 25_12 | 单元状态不一致 |
| 13 | 25_13 | RS485通信超时故障 |
| 14 | 25_14 | RS485通信校验故障 |
| 15 | 25_15 | DRAM读超时故障 |

#### 完整故障点表说明
完整的故障点表包含24_0到56_15共528个故障点（33组×16位），每个故障点对应具体的故障描述。

**主要故障点分组：**
- **24_0-24_15**：电网电压、频率保护和SVG输出电流保护
- **25_0-25_15**：SVG电流保护、通信故障和系统故障
- **26_0-27_15**：系统硬件故障、变压器保护和水冷系统故障
- **28_0-33_15**：A相功率单元故障（A1-A6相控制器和PWM板故障）
- **34_0-37_15**：三相PWM板硬件故障和通信故障
- **38_0-38_15**：CPU铁电故障（大部分为备用）
- **39_0-39_15**：系统运行状态（起始、充电、自检、复位、就绪、运行、故障、高压等）
- **40_0-45_15**：B相功率单元故障（B1-B6相控制器和PWM板故障）
- **46_0-51_15**：C相功率单元故障（C1-C6相控制器和PWM板故障）
- **52_0-53_15**：备用故障点
- **54_0-56_15**：并联运行模式、远程控制和补偿模式状态

#### 解析验证示例
根据实际测试数据验证：
- **输入数据**：`0x0200` 和 `0x0400`
- **0x0200**：十进制512，二进制`0000001000000000`，第9位为1 → `24_9`备用位激活
- **0x0400**：十进制1024，二进制`0000010000000000`，第10位为1 → `39_10`合高压等待状态激活

**重要说明**：
- `24_9`为备用位，激活时无具体故障含义，可能用于系统内部状态指示
- `39_10`表示"合高压等待"状态，属于系统正常运行状态之一，不是故障状态

## 状态变化事件处理

### 合高压等待状态变化逻辑
1. **状态监测**：实时监控故障位状态变化
2. **变化检测**：比较当前状态与历史状态
3. **事件触发**：当故障位从0变为1或从1变为0时触发事件
4. **状态更新**：更新故障状态并记录时间戳

### 处理流程
```
1. 读取C1命令响应数据
2. 解析34个16位故障状态数据
3. 按位解析每个故障点状态
4. 与上次状态比较，检测变化
5. 生成状态变化事件
6. 更新故障状态数据库
7. 推送MQTT消息通知
```

## 数据格式说明

### 输入数据格式
- **来源**：`realtime_data.json`文件
- **字段**：`raw_hex`（原始十六进制数据）
- **过滤条件**：`command == "C1"` 且 `success == true`

### 输出数据格式
- **文件名**：`c1_parsed_data.json`
- **格式**：JSON数组，每个故障点一个对象
- **字段说明**：
  - `id`：点表编号（如"24_0", "24_1"等）
  - `name`：故障描述（如"电网电压有效值Ⅰ段过压报警"）
  - `value`：解析后的整数值（0或1）
  - `ts`：时间戳
  - `hex_value`：十六进制值（用于调试）

### 示例输出
```json
[
    {
        "id": "24_0",
        "name": "电网电压有效值Ⅰ段过压报警",
        "ts": "2025-08-27 19:24:22.761",
        "value": 0,
        "hex_value": "0x0000"
    },
    {
        "id": "24_9",
        "name": "直流母线欠压报警",
        "ts": "2025-08-27 19:24:22.761",
        "value": 1,
        "hex_value": "0x0002"
    }
]
```

## 错误处理

### 数据验证
1. **报文长度检查**：确保数据长度符合协议要求
2. **校验位验证**：验证报文完整性
3. **命令码确认**：确保为C1命令响应
4. **数据范围检查**：确保故障位值在有效范围内

### 异常处理
1. **数据缺失**：提供默认值0（无故障）
2. **格式错误**：记录错误日志并跳过处理
3. **解析失败**：返回错误信息并保持上次有效状态
4. **文件操作失败**：重试机制和错误恢复

## 使用说明

### 运行环境
- Python 3.7+
- 依赖库：json, datetime, logging

### 执行方式
```bash
python c1_data_parser.py
```

### 配置要求
- 确保`realtime_data.json`文件存在且包含C1命令数据
- 确保有写入`c1_parsed_data.json`文件的权限
- 配置适当的日志级别和输出路径
