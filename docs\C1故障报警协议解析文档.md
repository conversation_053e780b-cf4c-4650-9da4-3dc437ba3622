# C1故障报警协议解析文档

## 协议规范

### 基本信息
- **命令码**：C1（故障报警）
- **功能**：读取系统故障报警状态
- **数据类型**：位状态数据（每位表示一个故障点的状态）

### 通信格式

#### 发送格式
```
EB 90 01 01 C1 00 00 00 00 00 00 00 00 3E AA AB
```

#### 接收格式
```
EB 90 01 01 C1 44 00 00 00 [数据34*16bit] [校验位] AA AB
```

### 数据结构
- **数据长度**：34个16位数据（68字节）
- **数据解析**：每个16位数据对应一个故障点表项，按位解析故障状态
- **字节序**：小端序（低字节在前）
- **位序**：LSB优先（最低位为第0位）

## 解析示例

### 示例报文
```
EB 90 01 01 C1 44 00 00 00 00 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 88 AA AB
```

### 解析过程
1. **提取数据段**：从第9字节开始的68字节数据
2. **按16位分组**：每2字节组成一个16位数据
3. **位状态解析**：
   - `02 00` → 0x0002 → 二进制：0000 0000 0000 0010 → 第1位置1
   - `04 00` → 0x0004 → 二进制：0000 0000 0000 0100 → 第2位置1

### 解析结果
- **24_9**：故障点24的第9位置1（对应第1个16位数据的第1位）
- **39_10**：故障点39的第10位置1（对应第16个16位数据的第2位）

## 故障点表映射

### 映射规则
- **点表编号格式**：`起始点_位偏移`
- **起始点范围**：24-56（共33个起始点）
- **位偏移范围**：0-15（每个起始点16位）
- **总故障点数**：33 × 16 = 528个故障点

### 详细映射表

#### 第1组：24_0 到 24_15（第1个16位数据）
| 位偏移 | 点表编号 | 故障描述 |
|--------|----------|----------|
| 0 | 24_0 | 电网电压有效值Ⅰ段过压报警 |
| 1 | 24_1 | 电网电压有效值Ⅰ段欠压报警 |
| 2 | 24_2 | 电网电压有效值Ⅱ段过压报警 |
| 3 | 24_3 | 电网电压有效值Ⅱ段欠压报警 |
| 4 | 24_4 | 电网频率过高报警 |
| 5 | 24_5 | 电网频率过低报警 |
| 6 | 24_6 | 电网电压不平衡报警 |
| 7 | 24_7 | 电网电压谐波报警 |
| 8 | 24_8 | 直流母线过压报警 |
| 9 | 24_9 | 直流母线欠压报警 |
| 10 | 24_10 | 直流母线不平衡报警 |
| 11 | 24_11 | 单元直流侧过压报警 |
| 12 | 24_12 | 单元直流侧欠压报警 |
| 13 | 24_13 | 单元直流侧不平衡报警 |
| 14 | 24_14 | 单元输出过流报警 |
| 15 | 24_15 | 单元输出电流不平衡报警 |

#### 第2组：25_0 到 25_15（第2个16位数据）
| 位偏移 | 点表编号 | 故障描述 |
|--------|----------|----------|
| 0 | 25_0 | 变压器温度过高报警 |
| 1 | 25_1 | 电抗器温度过高报警 |
| 2 | 25_2 | 控制柜温度过高报警 |
| 3 | 25_3 | 功率单元温度过高报警 |
| 4 | 25_4 | 冷却系统故障报警 |
| 5 | 25_5 | 风机故障报警 |
| 6 | 25_6 | 门禁开关报警 |
| 7 | 25_7 | 急停按钮报警 |
| 8 | 25_8 | 接地故障报警 |
| 9 | 25_9 | 绝缘故障报警 |
| 10 | 25_10 | 通信故障报警 |
| 11 | 25_11 | 控制器故障报警 |
| 12 | 25_12 | 传感器故障报警 |
| 13 | 25_13 | 执行器故障报警 |
| 14 | 25_14 | 保护装置故障报警 |
| 15 | 25_15 | 监控系统故障报警 |

#### 完整故障点表说明
完整的故障点表包含24_0到56_15共544个故障点（33组×16位），每个故障点对应具体的故障描述。

**已定义的故障点（前32个）：**
- **24_0-24_15**：电网和直流系统相关报警
- **25_0-25_15**：温度、冷却和安全系统相关报警

**通用故障点（26_0-56_15）：**
- 其余512个故障点使用通用命名格式：`故障点{组号}_{位号}报警`
- 可根据实际系统需求扩展具体的故障描述

#### 解析验证示例
根据实际测试数据验证：
- **输入数据**：`0x0200` 和 `0x0400`
- **0x0200**：十进制512，二进制`0000001000000000`，第9位为1 → `24_9`故障激活
- **0x0400**：十进制1024，二进制`0000010000000000`，第10位为1 → `39_10`故障激活

## 状态变化事件处理

### 合高压等待状态变化逻辑
1. **状态监测**：实时监控故障位状态变化
2. **变化检测**：比较当前状态与历史状态
3. **事件触发**：当故障位从0变为1或从1变为0时触发事件
4. **状态更新**：更新故障状态并记录时间戳

### 处理流程
```
1. 读取C1命令响应数据
2. 解析34个16位故障状态数据
3. 按位解析每个故障点状态
4. 与上次状态比较，检测变化
5. 生成状态变化事件
6. 更新故障状态数据库
7. 推送MQTT消息通知
```

## 数据格式说明

### 输入数据格式
- **来源**：`realtime_data.json`文件
- **字段**：`raw_hex`（原始十六进制数据）
- **过滤条件**：`command == "C1"` 且 `success == true`

### 输出数据格式
- **文件名**：`c1_parsed_data.json`
- **格式**：JSON数组，每个故障点一个对象
- **字段说明**：
  - `id`：点表编号（如"24_0", "24_1"等）
  - `name`：故障描述（如"电网电压有效值Ⅰ段过压报警"）
  - `value`：解析后的整数值（0或1）
  - `ts`：时间戳
  - `hex_value`：十六进制值（用于调试）

### 示例输出
```json
[
    {
        "id": "24_0",
        "name": "电网电压有效值Ⅰ段过压报警",
        "ts": "2025-08-27 19:24:22.761",
        "value": 0,
        "hex_value": "0x0000"
    },
    {
        "id": "24_9",
        "name": "直流母线欠压报警",
        "ts": "2025-08-27 19:24:22.761",
        "value": 1,
        "hex_value": "0x0002"
    }
]
```

## 错误处理

### 数据验证
1. **报文长度检查**：确保数据长度符合协议要求
2. **校验位验证**：验证报文完整性
3. **命令码确认**：确保为C1命令响应
4. **数据范围检查**：确保故障位值在有效范围内

### 异常处理
1. **数据缺失**：提供默认值0（无故障）
2. **格式错误**：记录错误日志并跳过处理
3. **解析失败**：返回错误信息并保持上次有效状态
4. **文件操作失败**：重试机制和错误恢复

## 使用说明

### 运行环境
- Python 3.7+
- 依赖库：json, datetime, logging

### 执行方式
```bash
python c1_data_parser.py
```

### 配置要求
- 确保`realtime_data.json`文件存在且包含C1命令数据
- 确保有写入`c1_parsed_data.json`文件的权限
- 配置适当的日志级别和输出路径
