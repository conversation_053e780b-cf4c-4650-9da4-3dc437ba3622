# C4 DSP状态协议解析文档

## 1. 协议概述

本文档详细描述了C4协议的数据格式和解析方法，用于查询DSP设备的实时状态数据。该协议可获取包括电压、电流、功率等在内的38个模拟量参数，为系统监控和诊断提供重要数据支持。

## 2. 协议基本格式

### 2.1 协议结构

协议遵循统一的数据帧格式：

| 字段 | 长度 | 说明 |
|------|------|------|
| 帧头 | 2字节 | 固定为 `EB 90` |
| 地址 | 1字节 | 固定为 `01` |
| 功能码 | 1字节 | 固定为 `01` |
| 命令码 | 1字节 | 固定为 `C4` |
| 数据长度 | 1字节 | 发送时为`00`，接收时为`C0` |
| 数据区 | 8字节(发送)/192字节(接收) | 发送时固定为 `00 00 00 00 00 00 00 00`，接收时为48个32位浮点数 |
| 校验码 | 1字节 | 根据命令计算 |
| 帧尾 | 2字节 | 固定为 `AA AB` |

## 3. 协议详细说明

### 3.1 C4协议 - DSP状态查询

#### 3.1.1 发送命令
```
EB 90 01 01 C4 00 00 00 00 00 00 00 00 41 AA AB
```

#### 3.1.2 接收响应格式
```
EB 90 01 01 C4 C0 00 00 00 [48个32位数据] [校验位] AA AB
```

#### 3.1.3 响应示例
```
EB 90 01 01 C4 C0 00 00 00 5D A5 89 3A F5 C9 4A 3A F9 02 04 3A 8E F4 88 B9 21 0B 8A 3A E1 93 DA 3A 31 3E 52 3B 48 2C 1E 3B 41 E5 5A B9 FB 56 B2 39 57 52 CB 34 FA D3 3B 3A 60 BD E7 3B CC 53 A8 39 7E 42 4E 3A 9C CF 2C B9 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 CD CC CC 3D E0 2D 2C 3F 00 00 00 00 00 00 00 00 00 00 00 00 1B F3 2C 36 8D 96 02 38 4B 51 BB 39 BF 4A 83 3A 00 00 00 00 00 00 00 00 B0 03 FE 39 B8 1E 85 3F FF 8F CD 3A FF 8F CD BA EC 97 FF BF 6F 32 7F 3F 14 DD 88 3A 38 00 48 3A 50 2A 03 3A 00 00 00 00 E1 93 DA 3A 31 3E 52 3B 48 2C 1E 3B C1 4D D2 3A 42 10 4C 3B 43 CB 1C 3B 53 AA AB
```

## 4. 数据解析方法

### 4.1 数据格式说明

响应数据中包含48个32位IEEE754浮点数，每个浮点数占用4字节，采用小端序（Little Endian）格式存储。

#### 4.1.1 IEEE754浮点数解析

以示例中的数据 `3A 89 A5 5D` 为例：

1. 按小端序重新排列：`5D A5 89 3A`
2. 转换为二进制：`01011101 10100101 10001001 00111010`
3. 符号位(S)：0（正数）
4. 指数位(E)：01110100（116）
5. 尾数位(M)：1011101 10100101 10001001
6. 计算实际值：(-1)^S × 2^(E-127) × (1.M) = (-1)^0 × 2^(116-127) × (1.M) ≈ 0.0010502

这个值表示AB线电压为0.0010502V。

### 4.2 数据映射表

响应数据中的48个32位浮点数对应以下38个参数（部分地址未使用）：

| 数据地址(十进制) | 参数名称 | 单位 | 描述 |
|-----------------|----------|------|------|
| 065 | AB线电压 | V | 线电压AB相 |
| 067 | BC线电压 | V | 线电压BC相 |
| 069 | CA线电压 | V | 线电压CA相 |
| 071 | 电压Ud | V | d轴电压分量 |
| 073 | 电压Uq | V | q轴电压分量 |
| 075 | A相SVG电流有效值 | A | A相静止无功发生器电流 |
| 077 | B相SVG电流有效值 | A | B相静止无功发生器电流 |
| 079 | C相SVG电流有效值 | A | C相静止无功发生器电流 |
| 081 | SVG输出有功 | kW | 静止无功发生器有功功率输出 |
| 083 | SVG输出无功电流 | A | 静止无功发生器无功电流输出 |
| 085 | 负载有功电流 | A | 负载侧有功电流 |
| 087 | 负载无功电流 | A | 负载侧无功电流 |
| 089 | 网侧电压幅度 | V | 电网侧电压幅值 |
| 091 | 高压侧电压幅度 | V | 高压侧电压幅值 |
| 093 | 上级母线电压Ud | V | 上级母线d轴电压 |
| 095 | 上级母线电压Uq | V | 上级母线q轴电压 |
| 097 | A相单元平均电压 | V | A相功率单元平均电压 |
| 099 | B相单元平均电压 | V | B相功率单元平均电压 |
| 101 | C相单元平均电压 | V | C相功率单元平均电压 |
| 103 | 三相单元平均电压 | V | 三相功率单元平均电压 |
| 105 | Id电流环PI调节器积分输出 | - | d轴电流环积分调节输出 |
| 107 | Iq电流环PI调节器积分输出 | - | q轴电流环积分调节输出 |
| 109 | 主控CPU时间利用率 | % | 主控制器CPU使用率 |
| 111 | 故障自动启动次数 | 次 | 系统故障后自动重启计数 |
| 113 | 远程SVG输出无功 | kVar | 远程控制SVG无功功率输出 |
| 115 | 电压无功模式电压调节器输出 | - | 电压无功控制模式调节器输出 |
| 117 | 负序电压d轴分量 | V | 负序电压d轴分量 |
| 119 | 负序电压q轴分量 | V | 负序电压q轴分量 |
| 121 | 负序电流d轴分量 | A | 负序电流d轴分量 |
| 123 | 负序电流q轴分量 | A | 负序电流q轴分量 |
| 125 | 谐波电流PI调节器d轴分量 | - | 谐波电流PI调节器d轴分量输出 |
| 127 | 谐波电流PI调节器q轴分量 | - | 谐波电流PI调节器q轴分量输出 |
| 129 | 电压调节器反馈值 | V | 电压调节器反馈值 |
| 131 | 电压调节器参考值 | V | 电压调节器参考值 |
| 133 | A相SVG电流有效值CT采样 | A | A相静止无功发生器电流CT采样值 |
| 135 | B相SVG电流有效值CT采样 | A | B相静止无功发生器电流CT采样值 |
| 137 | C相SVG电流有效值CT采样 | A | C相静止无功发生器电流CT采样值 |
| 139 | SVG零序电流有效值CT采样 | A | 静止无功发生器零序电流CT采样值 |

## 5. 使用示例

### 5.1 发送查询命令

向设备发送以下命令查询DSP状态：
```
EB 90 01 01 C4 00 00 00 00 00 00 00 00 41 AA AB
```

### 5.2 解析响应数据

1. 接收到响应数据后，首先验证帧头（`EB 90`）、地址（`01`）、功能码（`01`）和命令码（`C4`）是否正确
2. 验证数据长度是否为`C0`（192字节，对应48个32位浮点数）
3. 提取数据区域中的48个32位浮点数
4. 根据数据映射表，将对应位置的浮点数解析为相应的参数值

### 5.3 Python解析示例

```python
import struct

def parse_c4_response(response_hex):
    # 移除空格，转换为字节数组
    response_bytes = bytes.fromhex(response_hex.replace(" ", ""))
    
    # 验证帧头和命令码
    if response_bytes[0:2] != b'\xEB\x90' or response_bytes[4] != 0xC4:
        return "无效的响应数据"
    
    # 提取数据区域（从第9个字节开始，共192字节）
    data_section = response_bytes[9:9+192]
    
    # 解析参数
    parameters = {}
    parameter_map = {
        65: "AB线电压",
        67: "BC线电压",
        69: "CA线电压",
        # ... 其他参数映射
    }
    
    for addr, name in parameter_map.items():
        # 计算字节偏移（每个参数4字节）
        offset = (addr - 65) * 4
        if offset >= 0 and offset < len(data_section):
            # 使用struct解析IEEE754浮点数
            value = struct.unpack('<f', data_section[offset:offset+4])[0]
            parameters[name] = value
    
    return parameters

# 使用示例
response = "EB 90 01 01 C4 C0 00 00 00 5D A5 89 3A ... AA AB"
result = parse_c4_response(response)
print(result)
```

## 6. 错误处理

### 6.1 常见错误

| 错误类型 | 可能原因 | 处理方法 |
|----------|----------|----------|
| 无响应 | 设备离线或通信中断 | 检查设备连接和电源状态 |
| 帧头错误 | 数据传输错误或干扰 | 重新发送查询命令 |
| 校验和错误 | 数据传输过程中出现位错误 | 重新发送查询命令 |
| 数据长度错误 | 接收数据不完整 | 检查通信稳定性，重新查询 |

### 6.2 数据有效性验证

在实际应用中，应对解析出的参数值进行有效性验证：

1. 检查数值是否在合理范围内
2. 对于关联参数（如三相电压），检查其相互关系是否合理
3. 对于异常值，可能需要多次查询确认或与历史数据比对

## 7. 注意事项

1. 所有浮点数采用IEEE754标准和小端序格式，解析时需注意字节顺序
2. 部分参数地址未使用，解析时应跳过这些地址
3. 在高干扰环境下，可能需要增加通信重试机制
4. 建议定期查询以监控设备状态，但避免过于频繁的查询占用通信带宽
5. 对于关键参数，可设置阈值报警机制