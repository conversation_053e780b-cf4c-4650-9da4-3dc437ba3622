#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D5命令数据解析器
根据D3-D4-D5单元状态协议解析文档将十六进制数据转换为C相单元状态JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional


class D5DataParser:
    """
    D5命令数据解析器类
    用于解析D5命令返回的C相单元状态数据
    """
    
    def __init__(self):
        """
        初始化解析器，定义C相单元映射关系和状态码含义
        """
        # C相单元映射（根据物模型表）
        self.unit_mapping = {
            1: {"id": "HMI_30690", "name": "单元信息C1"},
            2: {"id": "HMI_30691", "name": "单元信息C2"},
            3: {"id": "HMI_30692", "name": "单元信息C3"},
            4: {"id": "HMI_30693", "name": "单元信息C4"},
            5: {"id": "HMI_30694", "name": "单元信息C5"},
            6: {"id": "HMI_30695", "name": "单元信息C6"},
            7: {"id": "HMI_30696", "name": "单元信息C7"},
            8: {"id": "HMI_30697", "name": "单元信息C8"},
            9: {"id": "HMI_30698", "name": "单元信息C9"},
            10: {"id": "HMI_30699", "name": "单元信息C10"},
            11: {"id": "HMI_30700", "name": "单元信息C11"},
            12: {"id": "HMI_30701", "name": "单元信息C12"}
        }
        
        # 状态码定义
        self.status_codes = {
            0x0001: "运行",
            0x0002: "故障",
            0x0004: "停止",
            0x0008: "电源故障",
            0x0010: "下行光纤断",
            0x0020: "封锁",
            0x0040: "旁路"
        }
    
    def extract_data_fields(self, raw_hex: str) -> Dict[str, Any]:
        """
        从raw_hex数据中提取data_hex和data_hex_formatted字段

        Args:
            raw_hex: 原始十六进制字符串

        Returns:
            dict: 包含data_hex和data_hex_formatted的字典
        """
        try:
            # 移除空格并转换为字节数组
            clean_hex = raw_hex.replace(" ", "")
            if len(clean_hex) % 2 != 0:
                raise ValueError("十六进制字符串长度必须为偶数")

            data = bytes.fromhex(clean_hex)

            # 验证最小长度（帧头+地址+功能码+命令码+数据长度+数据+校验+帧尾）
            if len(data) < 9:
                raise ValueError(f"数据长度不足，至少需要9字节，实际为{len(data)}字节")

            # 验证帧头帧尾
            if data[0:4] != b'\xEB\x90\x01\x01':
                raise ValueError("无效的帧头")
            if data[-2:] != b'\xAA\xAB':
                raise ValueError("无效的帧尾")

            # 验证命令码
            if data[4] != 0xD5:
                raise ValueError(f"命令码不匹配，期望0xD5，实际为0x{data[4]:02X}")

            # 获取数据长度
            data_length = data[5]
            if data_length != 0x24:  # 36字节
                raise ValueError(f"数据长度不匹配，期望0x24，实际为0x{data_length:02X}")

            # 根据协议提取数据部分
            # D5命令的数据从第9字节开始（帧头4 + 命令1 + 数据长度1 + 空字节占位3 = 9）
            data_start = 9
            data_end = data_start + data_length
            data_bytes = data[data_start:data_end]

            # 生成data_hex字段（原始十六进制数据）
            data_hex = data_bytes.hex().upper()

            # 生成data_hex_formatted字段（按2字节一组分为16位值）
            data_hex_formatted = []
            if len(data_bytes) % 2 == 0:
                # D5命令：按2字节一组分为16位值（小端序）
                for i in range(0, len(data_bytes), 2):
                    value = int.from_bytes(data_bytes[i:i+2], byteorder='little')
                    data_hex_formatted.append(f'0x{value:04X}')
            else:
                # 如果字节数为奇数，按字节显示
                data_hex_formatted = [f'0x{b:02X}' for b in data_bytes]

            return {
                'data_hex': data_hex,
                'data_hex_formatted': data_hex_formatted,
                'success': True
            }

        except Exception as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': str(e)
            }

    def parse_status_from_formatted_data(self, data_hex_formatted: List[str]) -> Dict[str, Any]:
        """
        基于data_hex_formatted数据进行状态解析处理
        根据D3-D4-D5单元状态协议解析文档进行解析

        Args:
            data_hex_formatted: 格式化后的十六进制数据列表

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            if not data_hex_formatted or len(data_hex_formatted) < 12:
                raise ValueError(f"数据不足，至少需要12个16位值，实际为{len(data_hex_formatted)}个")

            # 解析每个单元的状态（前12个单元为有效数据）
            unit_statuses = []
            for i in range(min(12, len(data_hex_formatted))):
                try:
                    # 从格式化的十六进制字符串中提取数值
                    hex_str = data_hex_formatted[i]
                    if hex_str.startswith('0x') or hex_str.startswith('0X'):
                        value = int(hex_str, 16)
                    else:
                        value = int(hex_str, 16)

                    # 解析状态码
                    status_list = []
                    for code, desc in self.status_codes.items():
                        if value & code:
                            status_list.append(desc)
                    
                    # 如果没有匹配任何状态，则标记为未知
                    if not status_list:
                        status_list.append("未知状态")
                    
                    # 判断旁路状态（11-12单元数据为0表示旁路）
                    if value == 0 and i >= 10:
                        status_list = ["旁路"]

                    unit_statuses.append({
                        'unit': i + 1,
                        'status_code': value,
                        'status_list': status_list,
                        'status_text': ", ".join(status_list),
                        'raw_value': value,
                        'hex_value': hex_str
                    })

                except (ValueError, IndexError) as e:
                    # 单个数据解析失败时，记录错误但继续处理其他数据
                    unit_statuses.append({
                        'unit': i + 1,
                        'status_code': 0,
                        'status_list': ["解析错误"],
                        'status_text': "解析错误",
                        'raw_value': 0,
                        'hex_value': data_hex_formatted[i] if i < len(data_hex_formatted) else 'N/A',
                        'error': str(e)
                    })

            return {
                'phase': 'C',
                'command': 'D5',
                'unit_statuses': unit_statuses,
                'total_units': len(unit_statuses),
                'running_units': len([s for s in unit_statuses if "运行" in s['status_list']]),
                'stopped_units': len([s for s in unit_statuses if "停止" in s['status_list']]),
                'fault_units': len([s for s in unit_statuses if "故障" in s['status_list'] or "电源故障" in s['status_list']]),
                'bypass_units': len([s for s in unit_statuses if "旁路" in s['status_list']]),
                'success': True
            }

        except Exception as e:
            return {
                'phase': 'C',
                'command': 'D5',
                'unit_statuses': [],
                'success': False,
                'error': str(e)
            }

    def parse_status_response(self, raw_hex: str) -> Dict[str, Any]:
        """
        解析D5协议的状态响应数据
        整合数据提取和状态解析功能

        Args:
            raw_hex: 原始十六进制字符串

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            # 第一步：提取data_hex和data_hex_formatted字段
            extract_result = self.extract_data_fields(raw_hex)
            if not extract_result['success']:
                return {
                    'phase': 'C',
                    'command': 'D5',
                    'unit_statuses': [],
                    'raw_data': raw_hex,
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': extract_result.get('error', '数据提取失败')
                }

            # 第二步：解析状态数据
            status_result = self.parse_status_from_formatted_data(extract_result['data_hex_formatted'])
            
            # 合并结果
            result = {
                'phase': 'C',
                'command': 'D5',
                'raw_data': raw_hex,
                'data_hex': extract_result['data_hex'],
                'data_hex_formatted': extract_result['data_hex_formatted'],
                'unit_statuses': status_result.get('unit_statuses', []),
                'total_units': status_result.get('total_units', 0),
                'running_units': status_result.get('running_units', 0),
                'stopped_units': status_result.get('stopped_units', 0),
                'fault_units': status_result.get('fault_units', 0),
                'bypass_units': status_result.get('bypass_units', 0),
                'timestamp': datetime.now().isoformat(),
                'success': status_result.get('success', False)
            }

            if not status_result.get('success', False):
                result['error'] = status_result.get('error', '状态解析失败')

            return result

        except Exception as e:
            return {
                'phase': 'C',
                'command': 'D5',
                'raw_data': raw_hex,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def parse_d5_data(self, raw_hex: str, timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        解析D5命令的raw_hex数据
        整合数据提取和状态解析功能，生成MQTT消息格式

        Args:
            raw_hex: D5命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间

        Returns:
            包含C相单元状态的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        result = []

        if not raw_hex:
            raise ValueError("D5命令数据格式错误，数据不能为空")

        try:
            # 解析数据
            parsed_data = self.parse_status_response(raw_hex)

            # 检查解析是否成功
            if not parsed_data.get('success', False):
                error_msg = parsed_data.get('error', '未知解析错误')
                raise ValueError(f"D5命令数据解析失败: {error_msg}")

            # 生成MQTT消息格式
            for status_info in parsed_data.get('unit_statuses', []):
                unit_num = status_info['unit']
                if unit_num in self.unit_mapping:
                    unit_config = self.unit_mapping[unit_num]

                    # 构建基本信息
                    unit_data = {
                        "id": unit_config["id"],
                        "name": unit_config["name"],
                        "ts": timestamp,
                        "value": status_info['status_code']  # 状态码整体作为value字段的值
                    }

                    # 添加十六进制值（可选）
                    if status_info.get('hex_value'):
                        unit_data['hex_value'] = status_info['hex_value']

                    result.append(unit_data)

            return result

        except Exception as e:
            # 增强错误处理，提供更详细的错误信息
            raise ValueError(f"D5命令数据解析过程异常: {str(e)}")
    
    def parse_d5_data_to_json(self, raw_hex: str, timestamp: Optional[str] = None,
                            output_file: Optional[str] = None, pretty_print: bool = True) -> str:
        """
        解析D5命令数据并输出为JSON字符串或保存到文件
        
        Args:
            raw_hex: D5命令返回的原始十六进制字符串
            timestamp: 时间戳
            output_file: 输出文件路径，如果不提供则返回JSON字符串
            pretty_print: 是否格式化输出JSON
            
        Returns:
            JSON字符串
        """
        parsed_data = self.parse_d5_data(raw_hex, timestamp)
        
        if pretty_print:
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)
        else:
            json_str = json.dumps(parsed_data, ensure_ascii=False)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"解析结果已保存到: {output_file}")
        
        return json_str


def main():
    """
    主函数，从realtime_data.json文件中读取D5命令数据并解析
    """
    parser = D5DataParser()

    # 读取realtime_data.json文件
    realtime_data_file = "data/realtime_data.json"

    try:
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("D5命令数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)

        # 查找D5命令数据
        d5_data_found = False
        for item in data:
            if item.get('command') == 'D5' and item.get('success'):
                d5_data_found = True

                print(f"找到D5命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")

                # 使用raw_hex进行完整解析
                raw_hex = item.get('raw_hex', '')
                if not raw_hex:
                    print("警告: raw_hex数据为空，跳过解析")
                    continue

                # 生成最终的MQTT格式数据
                print("生成MQTT格式数据并保存到文件")
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                
                # 调用parse_d5_data_to_json，同时完成显示和保存
                output_file = "data/d5_parsed_data.json"
                json_result = parser.parse_d5_data_to_json(
                    raw_hex,
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                print("MQTT格式解析结果:")
                print(json_result)

                break  # 只处理第一个找到的D5数据

        if not d5_data_found:
            print("在realtime_data.json中未找到有效的D5命令数据")
            print("请确保文件中包含command='D5'且success=true的数据项")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()