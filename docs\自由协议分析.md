# C0:IO状态 查询和返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C0
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C0
* **数据字节数**：0x08（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**：00 00 00 00 00 00 00 00 (共八个字节；前四个字节表示输入IO状态；后四个字节表示输出IO状态；小端模式DCBA)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# C1:故障报警 查询返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C1
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C1
* **数据字节数**：0x44（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**： (共68个字节；每2个字节一组；表示触摸屏30024号寄存器开始16bit信息，依次往后类推；小端模式BA)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# C3:故障录波 查询返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C3
* **数据字节数**：0xC8（单字节）
* **空字节占位**：00（单个空字节）
* **通道+数量**：00 00（双字节，举例：00 10 表示通道1从0开始50（200个字节）个浮点数据；C8 10 表示通道1从200开始后的50个浮点数据；00 20 表示通道2依此类推）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C3
* **数据字节数**：0xC8（单字节）
* **空字节占位**：00（单个空字节）
* **通道+数量**：00 00（双字节，举例：00 10 表示通道1从0开始50（200个字节）个浮点数据；C8 10 表示通道1从200开始后的50个浮点数据；00 20 表示通道2依此类推）
* **数据帧**：（共200个字节，每四个一组，表示一个浮点数，小端DCBA）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 录波规则
开始录波==>请求通道1第1包数据==>接收通道1第1包数据==>请求通道1第2包数据==>接收通道1第2包数据==>......依次类推....==>接收通道8第10包数据==>录波结束

# C4:DSP状态 查询返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C4
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C4
* **数据字节数**：0xC0（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**： (共192个字节；每4个字节一组；参照点表中DSP状态中的含义，依次往后类推；小端模式DCBA)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）


# C5:版本信息 查询返回帧规则(只在开机主动请求一次)
## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C5
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：C5
* **数据字节数**：0x30（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**： (共48个字节；每四个字节一组；分别对应：主控CPU板DSP版本号、主控CPU板CPLD版本号、主控A相PWM板DSP版本号、主控A相PWM板FPGA版本号、主控B相PWM板DSP版本号、主控B相PWM板FPGA版本号、主控C相PWM板DSP版本号、主控C相PWM板FPGA版本号、主控COMM板DSP版本号、主控COMM板CPLD版本号、主控CPU板FPGA版本号、主控CPU板子版本号；)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# 59：调试参数 查询帧，修改帧，修改返回帧，返回帧，修改完成确认帧，修改完成确认返回帧等规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：59（查询所有调试参数）
* **空字节占位**：00 00 00 00 00 00 00 00 （8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：57
* **数据字节数**：0x04（单字节）
* **空字节占位**：00 （单个空字节）
* **序号位**：00 00 （两个字节；小端模式BA）
* **数据帧**：00 00 00 00 (共4️个字节；小端模式DCBA；转换标准为IEEE754)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：55
* **空字节占位**：00 00（两个空字节）
* **序号位**：00 00 （两个字节；小端模式BA）
* **空字节占位**：00 00 00 00 (共4️个空字节；)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧（每帧返回一个序号的数据，总共返回259帧）
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：59
* **数据字节数**：0x04（单字节）
* **空字节占位**：00 （单个空字节）
* **序号位**：00 00 （两个字节；小端模式BA）
* **数据帧**：00 00 00 00 (共4️个字节；小端模式DCBA；转换标准为IEEE754)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改完成确认帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：37
* **空字节占位**：00 00 00 00 00 00 00 00 （8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改完成确认返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：5B
* **空字节占位**：00 00 00 00 00 00 00 00 （8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# 修改流程

**发送57修改命令**==>**接收55修改放回帧**==>**发送37修改完成确认命令**==>**接收5B修改完成确认帧**==>**发送59查询命令**==>**接收259帧放回数据**



# D0:A相单元直流侧电压 查询返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：D0
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：D0
* **数据字节数**：0x24（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**： (共36个字节；每2个字节一组；依次为第一单元电压，第二单元电压等；小端模式BA)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# D1:B相单元直流侧电压 查询返回帧规则(同D0)
# D2:C相单元直流侧电压 查询返回帧规则(同D0)

# D3:A相单元状态 查询返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：D0
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：D0
* **数据字节数**：0x24（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**： (共36个字节；每2个字节一组；依次为第一单元状态，第二单元状态等；小端模式BA)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# D4:A相单元状态 查询返回帧规则(同D3)
# D5:A相单元状态 查询返回帧规则(同D3)

# D6:A相单元程序版本信息 查询返回帧规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：D6
* **空字节占位**：00 00 00 00 00 00 00 00（8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：D6
* **数据字节数**：0x24（单字节）
* **空字节占位**：00 00 00（3个空字节）
* **数据帧**： (共36个字节；每2个字节一组；依次为第一单元版本信息，第二单元版本信息等；小端模式BA)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# D7:B相单元程序版本信息 查询返回帧规则（同D6命令规则）
# D8:C相单元程序版本信息 查询返回帧规则（同D6命令规则）


# 单字节和校验（HEX）计算规则为下：

1. **将所有待校验的十六进制字节转换为十进制数值**:例如，待校验字节为 EB 90 01 01，转换为十进制为：235、144、1、1。

2. **计算所有十进制数值的总和**:
总和 = 各字节十进制值相加
示例：235 + 144 + 1 + 1 = 381。

3. **对总和取 “低 8 位”（即对 256 取模）单字节校验位范围为 00 ~ FF（对应十进制 0 ~ 255），因此需通过 总和 % 256 截取低 8 位数值:**
示例：381 % 256 = 125（因为 256 × 1 = 256，381 - 256 = 125）。

4. **将结果转换为十六进制（HEX）得到的低 8 位十进制值转换为 2 位十六进制，不足 2 位补前导 0:**
示例：十进制 125 转换为十六进制为 7D，即校验位为 7D。

## 公式如下：
单字节和校验值（HEX）= （待校验字节的十进制总和 % 256）转换为 2 位十六进制

## 结果验证
以报文 EB 90 01 01 C0 00 00 00 00 00 00 00 00（前 13 字节）为例：

各字节十进制值：235、144、1、1、192、0、0、0、0、0、0、0、0

总和 = 235+144+1+1+192 = 573

取低 8 位：573 % 256 = 573 - 2×256 = 61

转换为十六进制：61 → 3D（与所给报文中的校验位完全一致）。


# 首页数据处理计算公式：
负载无功功率：（AB线电压*10*负载无功电流*额定电流*额定电压*√3）/10000000
负载有功功率：（AB线电压*10*负载有功电流*额定电流*额定电压*√3）/10000000
功率因数：负载有功功率/√（负载有功功率*负载有功功率+负载无功功率*负载无功功率）

母线电压Uab：AB线电压*10
母线电压Ubc：BC线电压*10
母线电压Uca：CA线电压*10

SVG电流Ia：A相SVG电流有效值*额定电流
SVG电流Ib：B相SVG电流有效值*额定电流
SVG电流Ic：C相SVG电流有效值*额定电流

网侧负载无功电流：负载无功电流*额定电流


SVG断路器输入对应拓扑图中的QF
启动柜接触器输入对应拓扑图中的KM
启动柜隔离刀闸输入对应拓扑中的QS
启动柜接地刀闸输入对应拓扑中的TS



上位机启动后的CMD顺序：C0→59→C5→D6→D7→D8→C1→C4→D0→D1→D2→C4→D3→D4→D5→C4→C0→C1→C4→D0→A1→D1→D2→C4→A2→D3→D4→D5→C4→C0→C1→C4→……

其中C0、59、C5、D6、D7、D8只在屏幕开机时按照这个顺序自动执行一遍,过后需要根据需求自己手动下发对应功能的指令

其中C1、C4、D0、D1、D2、C4、D3、D4、D5、C4、C0在未手动下发指令时，屏幕一直按照这个顺序循环下发指令和接收报文。当手动下发指令时，在执行完当前循环中正在进行的某一指令后优先执行手动下发的指令，执行完手动下发的指令后接着之前的顺序执行循环中的指令


