#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C5命令数据解析器
根据C5版本信息协议解析文档将十六进制数据转换为版本信息JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any


class C5DataParser:
    """
    C5命令数据解析器类
    用于解析C5命令返回的版本信息数据
    """
    
    def __init__(self):
        """
        初始化解析器，定义版本信息映射关系
        """
        # 版本信息模块映射（根据4.3节逐组解析）
        self.module_mapping = {
            1: "主控CPU板DSP版本号",
            2: "主控CPU板CPLD版本号",
            3: "主控A相PWM板DSP版本号",
            4: "主控A相PWM板FPGA版本号",
            5: "主控B相PWM板DSP版本号",
            6: "主控B相PWM板FPGA版本号",
            7: "主控C相PWM板DSP版本号",
            8: "主控C相PWM板FPGA版本号",
            # 组9-11：未定义模块（跳过解析）
            12: "复合版本信息"  # 特殊处理，包含主控CPU板IO板版本号和DSP子版本号
        }
    
    def bcd_to_decimal(self, bcd_byte: int) -> int:
        """
        将BCD码字节转换为十进制数
        
        Args:
            bcd_byte: BCD码字节
            
        Returns:
            int: 十进制数
        """
        return (bcd_byte >> 4) * 10 + (bcd_byte & 0x0F)
    
    def parse_standard_group(self, group_data: bytes) -> Dict[str, Any]:
        """
        解析标准4字节组数据
        
        Args:
            group_data: 4字节组数据
            
        Returns:
            dict: 解析结果
        """
        # 按实际字节顺序解析：第1个字节是版本号，后3个字节是日期
        version_byte = group_data[0]  # 第1个字节是版本号
        date_bytes = group_data[1:4]  # 后3个字节是日期
        
        # 版本号解析：十六进制转十进制显示
        if version_byte == 0x88:
            version_str = "8.8"
        else:
            major = version_byte >> 4  # 高4位
            minor = version_byte & 0x0F  # 低4位
            version_str = f"{major}.{minor}"
        
        # 日期解析：BCD码格式，YY-MM-DD
        if all(b == 0x88 for b in date_bytes):
            date_str = "88-88-88"
        elif all(b == 0x00 for b in date_bytes):
            date_str = "00-00-00"
        else:
            # BCD码转换
            yy = self.bcd_to_decimal(date_bytes[0])  # 年份
            mm = self.bcd_to_decimal(date_bytes[1])  # 月份
            dd = self.bcd_to_decimal(date_bytes[2])  # 日期
            date_str = f"{yy:02d}-{mm:02d}-{dd:02d}"
        
        return {
            'raw_bytes': ' '.join(f"{b:02X}" for b in group_data),
            'version': version_str,
            'date': date_str
        }
    
    def parse_group_12_composite(self, group_data: bytes) -> Dict[str, Any]:
        """
        解析第12组复合版本信息
        
        Args:
            group_data: 4字节组数据 (01 00 00 01)
            
        Returns:
            dict: 包含复合信息的解析结果
        """
        result = {}
        
        # 第12组特殊处理：复合版本信息
        # 字节1 (01): 跳过处理
        # 字节2 (00): 主控CPU板IO板版本号
        # 字节3-4 (00 01): 主控CPU板DSP子版本号
        
        # 子模块A：主控CPU板IO板版本号 (字节2)
        io_version_byte = group_data[1]
        io_major = (io_version_byte >> 4) & 0x0F
        io_minor = io_version_byte & 0x0F
        result["主控CPU板IO板版本号"] = {
            'raw_bytes': f"{io_version_byte:02X}",
            'version': f"{io_major}.{io_minor}",
            'date': ""  # 第12组不包含日期信息
        }
        
        # 子模块B：主控CPU板DSP子版本号 (字节3-4)
        dsp_version_bytes = group_data[2:4]
        dsp_major = self.bcd_to_decimal(dsp_version_bytes[0])
        dsp_minor = self.bcd_to_decimal(dsp_version_bytes[1])
        result["主控CPU板DSP子版本号"] = {
            'raw_bytes': ' '.join(f"{b:02X}" for b in dsp_version_bytes),
            'version': f"{dsp_major:02d}.{dsp_minor:02d}",
            'date': ""  # 第12组不包含日期信息
        }
        
        return result
    
    def parse_c5_data(self, data_hex_formatted: List[str], timestamp: str = None) -> List[Dict[str, Any]]:
        """
        解析C5命令的data_hex_formatted数据
        
        Args:
            data_hex_formatted: C5命令返回的格式化十六进制数据（12个32位十六进制值）
            timestamp: 时间戳，如果不提供则使用当前时间
            
        Returns:
            包含所有版本信息的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        
        result = []
        
        if len(data_hex_formatted) != 12:
            raise ValueError(f"C5命令数据格式错误，应包含12个32位十六进制值，实际收到{len(data_hex_formatted)}个")
        
        # 将12个32位十六进制值转换为字节数据（共48字节）
        version_data = bytes()
        for hex_str in data_hex_formatted:
            if hex_str.startswith('0x'):
                hex_str = hex_str[2:]
            if len(hex_str) == 8:  # 8个十六进制字符 = 4字节
                version_data += bytes.fromhex(hex_str)
            else:
                raise ValueError(f"无效的32位十六进制值: {hex_str}")
        
        if len(version_data) != 48:
            raise ValueError(f"版本信息数据长度错误，应为48字节，实际为{len(version_data)}字节")
        
        # 逐组解析，每组4字节
        for i in range(12):
            group_index = i + 1
            start_idx = i * 4
            group_data = version_data[start_idx:start_idx + 4]
            
            # 跳过未定义的模块
            if group_index not in self.module_mapping:
                continue
            
            if group_index == 12:
                # 第12组特殊处理：复合版本信息
                composite_info = self.parse_group_12_composite(group_data)
                for module_name, info in composite_info.items():
                    # 简化的英文标识符，去除main_ctrl_前缀
                    if "IO板版本号" in module_name:
                        module_key = "io_version"
                    elif "DSP子版本号" in module_name:
                        module_key = "dsp_sub_version"
                    else:
                        module_key = "unknown"
                    result.append({
                        "id": f"C5_{group_index}_{module_key}",
                        "name": module_name,
                        "ts": timestamp,
                        "value": info["version"]
                    })
            else:
                # 标准解析（组1-8） - 拆分成版本号和修改日期两条消息
                module_name = self.module_mapping[group_index]
                parsed_info = self.parse_standard_group(group_data)
                
                # 简化的英文标识符，去除main_ctrl_前缀
                if "CPU板DSP版本号" in module_name:
                    version_key = "cpu_dsp_version"
                    date_key = "cpu_dsp_date"
                elif "CPU板CPLD版本号" in module_name:
                    version_key = "cpu_cpld_version"
                    date_key = "cpu_cpld_date"
                elif "A相PWM板DSP版本号" in module_name:
                    version_key = "phase_a_pwm_dsp_version"
                    date_key = "phase_a_pwm_dsp_date"
                elif "A相PWM板FPGA版本号" in module_name:
                    version_key = "phase_a_pwm_fpga_version"
                    date_key = "phase_a_pwm_fpga_date"
                elif "B相PWM板DSP版本号" in module_name:
                    version_key = "phase_b_pwm_dsp_version"
                    date_key = "phase_b_pwm_dsp_date"
                elif "B相PWM板FPGA版本号" in module_name:
                    version_key = "phase_b_pwm_fpga_version"
                    date_key = "phase_b_pwm_fpga_date"
                elif "C相PWM板DSP版本号" in module_name:
                    version_key = "phase_c_pwm_dsp_version"
                    date_key = "phase_c_pwm_dsp_date"
                elif "C相PWM板FPGA版本号" in module_name:
                    version_key = "phase_c_pwm_fpga_version"
                    date_key = "phase_c_pwm_fpga_date"
                else:
                    version_key = f"group_{group_index}_version"
                    date_key = f"group_{group_index}_date"
                
                # 版本号消息
                result.append({
                    "id": f"C5_{group_index}_{version_key}",
                    "name": f"{module_name}",
                    "ts": timestamp,
                    "value": parsed_info["version"]
                })
                
                # 修改日期消息
                result.append({
                    "id": f"C5_{group_index}_{date_key}",
                    "name": f"{module_name}修改日期",
                    "ts": timestamp,
                    "value": parsed_info["date"]
                })
        
        return result
    
    def parse_c5_data_to_json(self, data_hex_formatted: List[str], timestamp: str = None,
                            output_file: str = None, pretty_print: bool = True) -> str:
        """
        解析C5命令数据并输出为JSON字符串或保存到文件
        
        Args:
            data_hex_formatted: C5命令返回的格式化十六进制数据
            timestamp: 时间戳
            output_file: 输出文件路径，如果不提供则返回JSON字符串
            pretty_print: 是否格式化输出JSON
            
        Returns:
            JSON字符串
        """
        parsed_data = self.parse_c5_data(data_hex_formatted, timestamp)
        
        if pretty_print:
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)
        else:
            json_str = json.dumps(parsed_data, ensure_ascii=False)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"解析结果已保存到: {output_file}")
        
        return json_str


def main():
    """
    主函数，从realtime_data.json文件中读取C5命令数据并解析
    """
    parser = C5DataParser()
    
    # 读取realtime_data.json文件
    realtime_data_file = "data/realtime_data.json"
    
    try:
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("C5命令数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)
        
        # 查找C5命令数据
        c5_data_found = False
        for item in data:
            if item.get('command') == 'C5' and item.get('success') and item.get('data_hex_formatted'):
                c5_data_found = True
                
                print(f"找到C5命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")
                print(f"格式化数据: {item.get('data_hex_formatted')}")
                print()
                
                # 解析数据
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                json_result = parser.parse_c5_data_to_json(
                    item['data_hex_formatted'],
                    timestamp=timestamp_str
                )
                print("解析结果:")
                print(json_result)
                
                # 保存到文件
                output_file = "data/c5_parsed_data.json"
                parser.parse_c5_data_to_json(
                    item['data_hex_formatted'],
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                
                break  # 只处理第一个找到的C5数据
        
        if not c5_data_found:
            print("在realtime_data.json中未找到有效的C5命令数据")
            print("请确保文件中包含command='C5'且success=true的数据项")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()