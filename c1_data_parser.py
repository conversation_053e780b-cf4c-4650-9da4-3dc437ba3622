#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C1故障报警协议解析器
根据C1故障报警协议解析文档将十六进制数据转换为故障状态JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional


class C1DataParser:
    """
    C1故障报警协议解析器类
    用于解析C1命令返回的故障报警状态数据
    """
    
    def __init__(self):
        """
        初始化解析器，定义故障点映射关系
        """
        # 故障点映射表（根据协议文档）
        self.fault_mapping = self._create_fault_mapping()
    
    def _create_fault_mapping(self) -> Dict[str, str]:
        """
        创建故障点映射表
        
        Returns:
            dict: 故障点ID到描述的映射
        """
        fault_map = {}
        
        # 第1组：24_0 到 24_15
        fault_descriptions_24 = [
            "电网电压有效值Ⅰ段过压报警", "电网电压有效值Ⅰ段欠压报警", "电网电压有效值Ⅱ段过压报警", "电网电压有效值Ⅱ段欠压报警",
            "电网频率过高报警", "电网频率过低报警", "电网电压不平衡报警", "电网电压谐波报警",
            "直流母线过压报警", "直流母线欠压报警", "直流母线不平衡报警", "单元直流侧过压报警",
            "单元直流侧欠压报警", "单元直流侧不平衡报警", "单元输出过流报警", "单元输出电流不平衡报警"
        ]
        
        # 第2组：25_0 到 25_15
        fault_descriptions_25 = [
            "变压器温度过高报警", "电抗器温度过高报警", "控制柜温度过高报警", "功率单元温度过高报警",
            "冷却系统故障报警", "风机故障报警", "门禁开关报警", "急停按钮报警",
            "接地故障报警", "绝缘故障报警", "通信故障报警", "控制器故障报警",
            "传感器故障报警", "执行器故障报警", "保护装置故障报警", "监控系统故障报警"
        ]
        
        # 添加24组故障点
        for i in range(16):
            fault_map[f"24_{i}"] = fault_descriptions_24[i]
        
        # 添加25组故障点
        for i in range(16):
            fault_map[f"25_{i}"] = fault_descriptions_25[i]
        
        # 添加其他组的故障点（26-56），使用通用描述
        for group in range(26, 57):  # 26到56
            for bit in range(16):
                fault_map[f"{group}_{bit}"] = f"故障点{group}_{bit}报警"
        
        return fault_map
    
    def extract_data_fields(self, raw_hex: str) -> Dict[str, Any]:
        """
        从raw_hex数据中提取C1命令的数据字段
        
        Args:
            raw_hex: 原始十六进制字符串
            
        Returns:
            dict: 包含data_hex和data_hex_formatted的字典
        """
        try:
            # 移除空格并转换为字节
            hex_clean = raw_hex.replace(' ', '').upper()
            if len(hex_clean) % 2 != 0:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '十六进制数据长度不是偶数'
                }
            
            data = bytes.fromhex(hex_clean)
            
            # 验证帧头和命令码
            if len(data) < 15:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '数据长度不足，无法解析C1命令'
                }
            
            # 检查帧头 EB 90 01 01
            if data[:4] != bytes([0xEB, 0x90, 0x01, 0x01]):
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '帧头不匹配，不是有效的协议帧'
                }
            
            # 检查命令码 C1
            if data[4] != 0xC1:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': '命令码不是C1'
                }
            
            # 获取数据长度（第5字节）
            data_length = data[5]
            if data_length != 0x44:  # C1命令数据长度应为68字节(0x44)
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': f'C1命令数据长度错误，期望68字节，实际{data_length}字节'
                }
            
            # C1命令的数据从第9字节开始（帧头4 + 命令1 + 数据长度1 + 空字节占位3 = 9）
            data_start = 9
            data_end = data_start + data_length
            
            if len(data) < data_end:
                return {
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': f'数据不完整，期望{data_end}字节，实际{len(data)}字节'
                }
            
            data_bytes = data[data_start:data_end]
            
            # 生成data_hex字段（原始十六进制数据）
            data_hex = data_bytes.hex().upper()
            
            # 生成data_hex_formatted字段（按2字节一组分为16位值，小端序）
            data_hex_formatted = []
            if len(data_bytes) % 2 == 0:
                # C1命令：按2字节一组分为16位值（小端序）
                for i in range(0, len(data_bytes), 2):
                    value = int.from_bytes(data_bytes[i:i+2], byteorder='little')
                    data_hex_formatted.append(f'0x{value:04X}')
            else:
                # 如果字节数为奇数，按字节显示
                data_hex_formatted = [f'0x{b:02X}' for b in data_bytes]
            
            return {
                'data_hex': data_hex,
                'data_hex_formatted': data_hex_formatted,
                'success': True
            }
            
        except ValueError as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': f'十六进制数据格式错误: {e}'
            }
        except Exception as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': f'数据提取失败: {e}'
            }
    
    def parse_fault_from_formatted_data(self, data_hex_formatted: List[str]) -> Dict[str, Any]:
        """
        从格式化的十六进制数据中解析故障状态
        
        Args:
            data_hex_formatted: 格式化的十六进制数据列表
            
        Returns:
            dict: 包含解析结果的字典
        """
        try:
            if len(data_hex_formatted) != 34:
                return {
                    'faults': [],
                    'total_fault_points': 0,
                    'active_faults': 0,
                    'success': False,
                    'error': f'C1命令数据应包含34个16位值，实际{len(data_hex_formatted)}个'
                }
            
            faults = []
            active_fault_count = 0
            
            # 解析34个16位数据
            for group_index, hex_value in enumerate(data_hex_formatted):
                try:
                    # 转换为整数
                    value = int(hex_value, 16)
                    
                    # 计算起始点编号（24 + group_index）
                    start_point = 24 + group_index
                    
                    # 解析16位中的每一位
                    for bit_index in range(16):
                        fault_id = f"{start_point}_{bit_index}"
                        bit_value = (value >> bit_index) & 1
                        
                        # 获取故障描述
                        fault_name = self.fault_mapping.get(fault_id, f"备份点{fault_id}")
                        
                        fault_info = {
                            'id': fault_id,
                            'name': fault_name,
                            'value': bit_value,
                            'hex_source': hex_value
                        }
                        
                        faults.append(fault_info)
                        
                        if bit_value == 1:
                            active_fault_count += 1
                            
                except ValueError as e:
                    return {
                        'faults': [],
                        'total_fault_points': 0,
                        'active_faults': 0,
                        'success': False,
                        'error': f'无法解析十六进制值 {hex_value}: {e}'
                    }
            
            return {
                'faults': faults,
                'total_fault_points': len(faults),
                'active_faults': active_fault_count,
                'success': True
            }
            
        except Exception as e:
            return {
                'faults': [],
                'total_fault_points': 0,
                'active_faults': 0,
                'success': False,
                'error': f'故障解析失败: {e}'
            }
    
    def parse_fault_response(self, raw_hex: str) -> Dict[str, Any]:
        """
        解析C1命令的完整响应数据
        整合数据提取和故障解析功能
        
        Args:
            raw_hex: C1命令返回的原始十六进制字符串
            
        Returns:
            dict: 包含解析结果的字典
        """
        try:
            # 第一步：提取data_hex和data_hex_formatted字段
            extract_result = self.extract_data_fields(raw_hex)
            if not extract_result['success']:
                return {
                    'command': 'C1',
                    'faults': [],
                    'raw_data': raw_hex,
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'total_fault_points': 0,
                    'active_faults': 0,
                    'success': False,
                    'error': f"数据提取失败: {extract_result.get('error', '未知错误')}"
                }
            
            # 第二步：基于data_hex_formatted进行故障解析
            parse_result = self.parse_fault_from_formatted_data(extract_result['data_hex_formatted'])
            if not parse_result['success']:
                return {
                    'command': 'C1',
                    'faults': [],
                    'raw_data': raw_hex,
                    'data_hex': extract_result['data_hex'],
                    'data_hex_formatted': extract_result['data_hex_formatted'],
                    'total_fault_points': 0,
                    'active_faults': 0,
                    'success': False,
                    'error': f"故障解析失败: {parse_result.get('error', '未知错误')}"
                }
            
            # 成功解析
            return {
                'command': 'C1',
                'faults': parse_result['faults'],
                'raw_data': raw_hex,
                'data_hex': extract_result['data_hex'],
                'data_hex_formatted': extract_result['data_hex_formatted'],
                'total_fault_points': parse_result['total_fault_points'],
                'active_faults': parse_result['active_faults'],
                'success': True
            }
            
        except Exception as e:
            return {
                'command': 'C1',
                'faults': [],
                'raw_data': raw_hex,
                'data_hex': '',
                'data_hex_formatted': [],
                'total_fault_points': 0,
                'active_faults': 0,
                'success': False,
                'error': f"解析过程异常: {e}"
            }

    def parse_c1_data(self, raw_hex: str, timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        解析C1命令的raw_hex数据
        整合数据提取和故障解析功能，生成MQTT消息格式

        Args:
            raw_hex: C1命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间

        Returns:
            包含故障状态的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        result = []

        if not raw_hex:
            raise ValueError("C1命令数据格式错误，数据不能为空")

        try:
            # 解析数据（使用新的整合方法）
            parsed_data = self.parse_fault_response(raw_hex)

            # 检查解析是否成功
            if not parsed_data.get('success', False):
                error_msg = parsed_data.get('error', '未知解析错误')
                raise ValueError(f"C1命令数据解析失败: {error_msg}")

            # 转换为MQTT格式
            for fault in parsed_data['faults']:
                fault_item = {
                    "id": fault['id'],
                    "name": fault['name'],
                    "ts": timestamp,
                    "value": fault['value'],
                    "hex_value": fault['hex_source']
                }
                result.append(fault_item)

            return result

        except Exception as e:
            raise ValueError(f"C1命令数据解析异常: {e}")

    def parse_c1_data_to_json(self, raw_hex: str, timestamp: Optional[str] = None,
                             output_file: Optional[str] = None) -> str:
        """
        解析C1命令数据并输出为JSON格式

        Args:
            raw_hex: C1命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间
            output_file: 输出文件路径，如果提供则保存到文件

        Returns:
            JSON格式的字符串
        """
        try:
            # 解析数据
            parsed_data = self.parse_c1_data(raw_hex, timestamp)

            # 转换为JSON字符串
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)

            # 如果指定了输出文件，则保存
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(json_str)
                print(f"C1解析结果已保存到: {output_file}")

            return json_str

        except Exception as e:
            error_msg = f"C1数据解析到JSON失败: {e}"
            print(error_msg)
            return json.dumps({"error": error_msg}, ensure_ascii=False, indent=4)


def main():
    """
    主函数：从realtime_data.json读取C1命令数据并进行解析
    """
    realtime_data_file = "data/realtime_data.json"

    try:
        # 读取realtime_data.json
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        parser = C1DataParser()

        print("C1故障报警数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)

        # 查找C1命令数据
        c1_data_found = False
        for item in data:
            if item.get('command') == 'C1' and item.get('success'):
                c1_data_found = True

                print(f"找到C1命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")

                # 如果存在data_hex_formatted，显示它
                if item.get('data_hex_formatted'):
                    print(f"已有格式化数据: {item.get('data_hex_formatted')}")

                print()

                # 使用raw_hex进行完整解析（包含数据提取和故障解析）
                raw_hex = item.get('raw_hex', '')
                if not raw_hex:
                    print("警告: raw_hex数据为空，跳过解析")
                    continue

                # 演示数据提取功能
                print("步骤1: 数据提取")
                extract_result = parser.extract_data_fields(raw_hex)
                if extract_result['success']:
                    print(f"  data_hex: {extract_result['data_hex']}")
                    print(f"  data_hex_formatted: {extract_result['data_hex_formatted'][:5]}...（显示前5个）")
                else:
                    print(f"  数据提取失败: {extract_result.get('error')}")
                    continue

                print()

                # 演示基于格式化数据的故障解析
                print("步骤2: 故障解析")
                fault_result = parser.parse_fault_from_formatted_data(extract_result['data_hex_formatted'])
                if fault_result['success']:
                    print(f"  解析成功，共{fault_result['total_fault_points']}个故障点")
                    print(f"  活跃故障: {fault_result['active_faults']}个")
                    if fault_result['active_faults'] > 0:
                        print("  活跃故障列表:")
                        for fault in fault_result['faults']:
                            if fault['value'] == 1:
                                print(f"    {fault['id']}: {fault['name']}")
                else:
                    print(f"  故障解析失败: {fault_result.get('error')}")
                    continue

                print()

                # 生成最终的MQTT格式数据
                print("步骤3: 生成MQTT格式数据")
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

                # 只调用一次parse_c1_data_to_json，同时完成显示和保存
                output_file = "data/c1_parsed_data.json"
                json_result = parser.parse_c1_data_to_json(
                    raw_hex,
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                print("MQTT格式解析结果（显示前3个故障点）:")
                parsed_list = json.loads(json_result)
                for i, fault in enumerate(parsed_list[:3]):
                    print(f"  {fault}")
                if len(parsed_list) > 3:
                    print(f"  ... 还有{len(parsed_list) - 3}个故障点")

                break  # 只处理第一个找到的C1数据

        if not c1_data_found:
            print("在realtime_data.json中未找到有效的C1命令数据")
            print("请确保文件中包含command='C1'且success=true的数据项")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()
