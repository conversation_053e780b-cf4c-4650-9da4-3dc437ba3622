#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C1故障报警协议解析器测试脚本
验证解析结果的正确性
"""

from c1_data_parser import C1DataParser


def test_c1_parser():
    """
    测试C1解析器的功能
    """
    parser = C1DataParser()
    
    # 测试数据：来自realtime_data.json的实际C1数据
    test_raw_hex = "EB900101C144000000000200000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000000000088AAAB"
    
    print("C1故障报警协议解析器测试")
    print("=" * 50)
    print(f"测试数据: {test_raw_hex}")
    print()
    
    # 测试数据提取
    print("1. 测试数据提取功能")
    extract_result = parser.extract_data_fields(test_raw_hex)
    if extract_result['success']:
        print("✓ 数据提取成功")
        print(f"  数据长度: {len(extract_result['data_hex']) // 2} 字节")
        print(f"  16位数据个数: {len(extract_result['data_hex_formatted'])}")
        print(f"  前5个16位数据: {extract_result['data_hex_formatted'][:5]}")
    else:
        print("✗ 数据提取失败:", extract_result['error'])
        return
    
    print()
    
    # 测试故障解析
    print("2. 测试故障解析功能")
    fault_result = parser.parse_fault_from_formatted_data(extract_result['data_hex_formatted'])
    if fault_result['success']:
        print("✓ 故障解析成功")
        print(f"  总故障点数: {fault_result['total_fault_points']}")
        print(f"  活跃故障数: {fault_result['active_faults']}")
        
        # 显示活跃故障
        if fault_result['active_faults'] > 0:
            print("  活跃故障详情:")
            for fault in fault_result['faults']:
                if fault['value'] == 1:
                    print(f"    {fault['id']}: {fault['name']} (来源: {fault['hex_source']})")
    else:
        print("✗ 故障解析失败:", fault_result['error'])
        return
    
    print()
    
    # 测试完整解析
    print("3. 测试完整解析功能")
    complete_result = parser.parse_fault_response(test_raw_hex)
    if complete_result['success']:
        print("✓ 完整解析成功")
        print(f"  命令: {complete_result['command']}")
        print(f"  总故障点: {complete_result['total_fault_points']}")
        print(f"  活跃故障: {complete_result['active_faults']}")
    else:
        print("✗ 完整解析失败:", complete_result['error'])
        return
    
    print()
    
    # 验证解析结果的正确性
    print("4. 验证解析结果")
    
    # 验证第一个活跃故障：24_9 (0x0200的第9位)
    # 0x0200 = 512 = 0000001000000000 (二进制)，第9位为1
    hex_val_1 = int(extract_result['data_hex_formatted'][0], 16)
    bit_9 = (hex_val_1 >> 9) & 1
    print(f"  验证24_9: 0x{hex_val_1:04X} 第9位 = {bit_9} ✓" if bit_9 == 1 else f"  验证24_9: 失败")
    
    # 验证第二个活跃故障：39_10 (第16个16位数据的第10位)
    # 39 = 24 + 15，所以是第16个数据 (索引15)
    hex_val_16 = int(extract_result['data_hex_formatted'][15], 16)
    bit_10 = (hex_val_16 >> 10) & 1
    print(f"  验证39_10: 0x{hex_val_16:04X} 第10位 = {bit_10} ✓" if bit_10 == 1 else f"  验证39_10: 失败")
    
    print()
    
    # 测试MQTT格式输出
    print("5. 测试MQTT格式输出")
    try:
        mqtt_data = parser.parse_c1_data(test_raw_hex, "2025-08-27 19:24:22.761")
        print(f"✓ MQTT格式生成成功，共{len(mqtt_data)}个故障点")
        
        # 显示活跃故障的MQTT格式
        active_faults = [fault for fault in mqtt_data if fault['value'] == 1]
        print(f"  活跃故障MQTT格式 ({len(active_faults)}个):")
        for fault in active_faults:
            print(f"    {fault}")
            
    except Exception as e:
        print(f"✗ MQTT格式生成失败: {e}")
    
    print()
    print("测试完成！")


def verify_bit_calculation():
    """
    验证位计算的正确性
    """
    print("\n位计算验证")
    print("=" * 30)
    
    # 测试数据：0x0200 和 0x0400
    test_cases = [
        {"hex": "0x0200", "decimal": 512, "expected_bit": 9},
        {"hex": "0x0400", "decimal": 1024, "expected_bit": 10}
    ]
    
    for case in test_cases:
        value = int(case["hex"], 16)
        print(f"十六进制: {case['hex']}")
        print(f"十进制: {value}")
        print(f"二进制: {bin(value)[2:].zfill(16)}")
        
        # 找到置位的位
        for bit in range(16):
            if (value >> bit) & 1:
                print(f"第{bit}位为1 {'✓' if bit == case['expected_bit'] else '✗'}")
        print()


if __name__ == "__main__":
    test_c1_parser()
    verify_bit_calculation()
