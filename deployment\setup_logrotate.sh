#!/bin/bash

# 日志轮转配置安装脚本
# 功能：安装和配置日志轮转规则

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]]; then
   log_error "此脚本需要root权限运行"
   exit 1
fi

# 配置文件路径
SOURCE_CONFIG="/home/<USER>/SerialPortRule/deployment/serialport-logrotate.conf"
TARGET_CONFIG="/etc/logrotate.d/serialport-service"

# 复制logrotate配置文件
log_info "安装logrotate配置文件..."
cp "$SOURCE_CONFIG" "$TARGET_CONFIG"
chmod 644 "$TARGET_CONFIG"

# 验证配置文件语法
log_info "验证logrotate配置语法..."
if logrotate -d "$TARGET_CONFIG" > /dev/null 2>&1; then
    log_info "配置文件语法正确"
else
    log_error "配置文件语法错误"
    logrotate -d "$TARGET_CONFIG"
    exit 1
fi

# 配置systemd journal日志轮转
log_info "配置systemd journal日志轮转..."
JOURNALD_CONF="/etc/systemd/journald.conf.d/serialport-service.conf"
mkdir -p /etc/systemd/journald.conf.d/

cat > "$JOURNALD_CONF" << EOF
# 串口通信服务journal日志配置
[Journal]
# 限制journal日志大小
SystemMaxUse=500M
SystemKeepFree=1G
SystemMaxFileSize=50M
SystemMaxFiles=10

# 日志保留时间
MaxRetentionSec=30day

# 压缩日志
Compress=yes

# 转发到syslog
ForwardToSyslog=no
ForwardToWall=no
EOF

# 重启systemd-journald服务
log_info "重启systemd-journald服务..."
systemctl restart systemd-journald

# 测试logrotate配置
log_info "测试logrotate配置..."
logrotate -f "$TARGET_CONFIG" || log_warn "logrotate测试可能失败，但配置已安装"

# 显示当前journal使用情况
log_info "当前journal磁盘使用情况："
journalctl --disk-usage

log_info "日志轮转配置完成！"
log_info "配置文件位置："
echo "  应用日志轮转: $TARGET_CONFIG"
echo "  系统日志配置: $JOURNALD_CONF"
log_info "手动执行日志轮转: logrotate -f $TARGET_CONFIG"
log_info "查看journal日志: journalctl -u serialport-service -f"
log_info "清理旧journal日志: journalctl --vacuum-time=7d"