#!/bin/bash

# 数据解析器服务管理脚本

SERVICE_NAME="data-parser"
SERVICE_FILE="deployment/data-parser.service"
SERVICE_PATH="/etc/systemd/system/data-parser.service"
WORK_DIR="/home/<USER>/SerialPortRule"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root权限运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root权限运行此脚本"
        echo "使用: sudo $0 $1"
        exit 1
    fi
}

# 安装服务
install_service() {
    print_info "安装数据解析器服务..."
    
    # 检查服务文件是否存在
    if [ ! -f "$SERVICE_FILE" ]; then
        print_error "服务文件 $SERVICE_FILE 不存在"
        exit 1
    fi
    
    # 复制服务文件
    cp "$SERVICE_FILE" "$SERVICE_PATH"
    if [ $? -eq 0 ]; then
        print_success "服务文件已复制到 $SERVICE_PATH"
    else
        print_error "复制服务文件失败"
        exit 1
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    if [ $? -eq 0 ]; then
        print_success "systemd配置已重新加载"
    else
        print_error "重新加载systemd配置失败"
        exit 1
    fi
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_success "服务已设置为开机自启"
    else
        print_warning "设置开机自启失败"
    fi
    
    print_success "数据解析器服务安装完成"
}

# 卸载服务
uninstall_service() {
    print_info "卸载数据解析器服务..."
    
    # 停止服务
    systemctl stop "$SERVICE_NAME" 2>/dev/null
    
    # 禁用服务
    systemctl disable "$SERVICE_NAME" 2>/dev/null
    
    # 删除服务文件
    if [ -f "$SERVICE_PATH" ]; then
        rm "$SERVICE_PATH"
        print_success "服务文件已删除"
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    
    print_success "数据解析器服务卸载完成"
}

# 启动服务
start_service() {
    print_info "启动数据解析器服务..."
    systemctl start "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_success "服务启动成功"
    else
        print_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_service() {
    print_info "停止数据解析器服务..."
    systemctl stop "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_success "服务停止成功"
    else
        print_error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart_service() {
    print_info "重启数据解析器服务..."
    systemctl restart "$SERVICE_NAME"
    if [ $? -eq 0 ]; then
        print_success "服务重启成功"
    else
        print_error "服务重启失败"
        exit 1
    fi
}

# 查看服务状态
status_service() {
    print_info "数据解析器服务状态:"
    systemctl status "$SERVICE_NAME" --no-pager
}

# 查看服务日志
logs_service() {
    print_info "数据解析器服务日志:"
    if [ "$2" = "-f" ]; then
        journalctl -u "$SERVICE_NAME" -f
    else
        journalctl -u "$SERVICE_NAME" --no-pager -n 50
    fi
}

# 显示帮助信息
show_help() {
    echo "数据解析器服务管理脚本"
    echo "用法: $0 {install|uninstall|start|stop|restart|status|logs|help}"
    echo ""
    echo "命令说明:"
    echo "  install    - 安装服务到系统（需要root权限）"
    echo "  uninstall  - 从系统卸载服务（需要root权限）"
    echo "  start      - 启动服务（需要root权限）"
    echo "  stop       - 停止服务（需要root权限）"
    echo "  restart    - 重启服务（需要root权限）"
    echo "  status     - 查看服务状态"
    echo "  logs       - 查看服务日志"
    echo "  logs -f    - 实时查看服务日志"
    echo "  help       - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  sudo $0 install    # 安装服务"
    echo "  sudo $0 start      # 启动服务"
    echo "  $0 status          # 查看状态"
    echo "  $0 logs -f         # 实时查看日志"
}

# 主逻辑
case "$1" in
    install)
        check_root
        install_service
        ;;
    uninstall)
        check_root
        uninstall_service
        ;;
    start)
        check_root
        start_service
        ;;
    stop)
        check_root
        stop_service
        ;;
    restart)
        check_root
        restart_service
        ;;
    status)
        status_service
        ;;
    logs)
        logs_service "$@"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac