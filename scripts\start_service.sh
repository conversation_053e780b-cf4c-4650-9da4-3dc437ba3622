#!/bin/bash

# 串口通信服务启动脚本
# 功能：启动串口通信服务并检查状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    printf "${GREEN}[INFO]${NC} %s\n" "$1"
}

log_warn() {
    printf "${YELLOW}[WARN]${NC} %s\n" "$1"
}

log_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# 服务名称
SERVICE_NAME="serialport-service"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
SOURCE_SERVICE_FILE="/home/<USER>/SerialPortRule/deployment/serialport-service.service"

# 检查是否以root权限运行
if [ "$(id -u)" -ne 0 ]; then
   log_error "此脚本需要root权限运行"
   exit 1
fi

# 复制服务文件到系统目录
log_info "复制服务文件到系统目录..."
cp "$SOURCE_SERVICE_FILE" "$SERVICE_FILE"

# 重新加载systemd配置
log_info "重新加载systemd配置..."
systemctl daemon-reload

# 启用服务（开机自启动）
log_info "启用服务开机自启动..."
systemctl enable "$SERVICE_NAME"

# 启动服务
log_info "启动串口通信服务..."
systemctl start "$SERVICE_NAME"

# 等待服务启动
sleep 3

# 检查服务状态
if systemctl is-active --quiet "$SERVICE_NAME"; then
    log_info "服务启动成功！"
    log_info "服务状态："
    systemctl status "$SERVICE_NAME" --no-pager -l
else
    log_error "服务启动失败！"
    log_error "服务状态："
    systemctl status "$SERVICE_NAME" --no-pager -l
    exit 1
fi

log_info "可以使用以下命令管理服务："
echo "  启动服务: systemctl start $SERVICE_NAME"
echo "  停止服务: systemctl stop $SERVICE_NAME"
echo "  重启服务: systemctl restart $SERVICE_NAME"
echo "  查看状态: systemctl status $SERVICE_NAME"
echo "  查看日志: journalctl -u $SERVICE_NAME -f"