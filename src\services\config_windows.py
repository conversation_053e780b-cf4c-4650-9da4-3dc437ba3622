#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows配置修改工具
用于修改串口号和其他Windows相关配置
"""

import os
import re

def modify_serial_port():
    """修改串口号"""
    config_file = 'config/service_windows.conf'
    
    if not os.path.exists(config_file):
        print("Windows配置文件不存在，请先创建...")
        return
    
    print("当前可用的Windows配置文件:")
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 查找当前的串口号
    port_match = re.search(r'port\s*=\s*"([^"]+)"', content)
    current_port = port_match.group(1) if port_match else "未知"
    print(f"当前串口号: {current_port}")
    
    # 获取用户输入的新串口号
    new_port = input("请输入新的串口号 (例如: COM3, COM4, COM5): ").strip()
    
    if not new_port.startswith('COM'):
        print("错误：串口号必须以COM开头，例如COM3")
        return
    
    # 替换串口号
    new_content = re.sub(r'port\s*=\s*"[^"]*"', f'port = "{new_port}"', content)
    
    # 写回文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"串口号已成功修改为: {new_port}")
    print("你可以现在运行 start_windows.bat 启动服务")

def show_help():
    """显示帮助信息"""
    print("""
Windows串口通信服务配置工具

使用方法:
1. 首先确认你的设备连接到哪个COM端口
   - 可以在设备管理器中查看
   - 通常是COM3、COM4、COM5等

2. 修改串口号:
   运行: python config_windows.py
   选择修改串口号，输入正确的COM端口号

3. 启动服务:
   双击运行: start_windows.bat
   或命令行运行: python simple_main.py

注意事项:
- 确保设备已正确连接
- 确保没有其他程序占用该串口
- 波特率等参数已在配置文件中预设
""")

if __name__ == "__main__":
    print("Windows串口通信服务配置工具")
    print("=" * 40)
    
    while True:
        print("\n选项:")
        print("1. 修改串口号")
        print("2. 查看帮助")
        print("3. 退出")
        
        choice = input("请选择操作 (1-3): ").strip()
        
        if choice == '1':
            modify_serial_port()
        elif choice == '2':
            show_help()
        elif choice == '3':
            break
        else:
            print("无效选择，请重新输入")