# 工具脚本使用说明

## 概述

本目录包含两个主要的服务管理脚本：
- `start_data_parser_service.sh` - 数据解析器服务启动脚本
- `manage_service.sh` - 系统服务管理脚本

## start_data_parser_service.sh

### 功能说明
- 自动检测和配置Python3环境
- 创建和管理虚拟环境
- 安装项目依赖
- 启动数据解析器服务

### 使用方法

**重要：必须使用bash运行，不要使用sh**

```bash
# 正确的运行方式
bash start_data_parser_service.sh

# 或者给脚本执行权限后直接运行
chmod +x start_data_parser_service.sh
./start_data_parser_service.sh
```

**错误的运行方式（会导致语法错误）：**
```bash
# 不要这样运行
sh start_data_parser_service.sh  # 会出现 "Bad substitution" 错误
```

### 脚本特性

1. **自动路径检测**：脚本可以从任何位置运行，会自动切换到项目根目录
2. **虚拟环境管理**：自动创建和激活Python虚拟环境，避免系统包冲突
3. **依赖管理**：自动检测和安装项目依赖包
4. **环境兼容**：支持不同的Python3安装路径

### 运行流程

1. 检测项目根目录
2. 检测Python3环境
3. 创建虚拟环境（如果不存在）
4. 激活虚拟环境
5. 检查和安装依赖包
6. 启动数据解析器服务

## manage_service.sh

### 功能说明
完整的系统服务管理工具，支持安装、启动、停止、重启等操作。

### 使用方法

```bash
# 查看帮助
./manage_service.sh help

# 安装服务（需要root权限）
sudo ./manage_service.sh install

# 启动服务（需要root权限）
sudo ./manage_service.sh start

# 查看服务状态
./manage_service.sh status

# 查看服务日志
./manage_service.sh logs

# 实时查看日志
./manage_service.sh logs -f

# 重启服务（需要root权限）
sudo ./manage_service.sh restart

# 停止服务（需要root权限）
sudo ./manage_service.sh stop

# 卸载服务（需要root权限）
sudo ./manage_service.sh uninstall
```

## 使用场景选择

### 开发和测试环境
推荐使用 `start_data_parser_service.sh`：
- 快速启动
- 便于调试
- 自动环境配置

### 生产环境
推荐使用 `manage_service.sh`：
- 系统服务管理
- 开机自启
- 日志管理
- 服务监控

## 常见问题

### Q: 运行脚本时出现 "Bad substitution" 错误
A: 请使用 `bash` 而不是 `sh` 运行脚本。脚本使用了bash特有的语法。

### Q: 出现 "externally-managed-environment" 错误
A: 这是Python 3.11+的新特性。脚本已经自动处理此问题，会创建虚拟环境来安装依赖。

### Q: 找不到Python3
A: 脚本会自动检测常见的Python3路径。如果仍然失败，请确保Python3已正确安装。

### Q: 权限问题
A: 某些操作需要root权限，请使用 `sudo` 运行相关命令。

## 技术说明

- 脚本使用bash语法，兼容性更好
- 自动创建Python虚拟环境，避免系统包冲突
- 支持从任意目录运行，自动路径检测
- 包含完整的错误处理和用户反馈