# MQTT统一配置管理系统使用说明

## 概述

本系统已将所有MQTT通道的配置信息统一管理，通过单一配置文件 `config/mqtt_config.json` 来管理所有命令通道（C0、59、D6、D7、D8）的MQTT连接参数。

## 配置文件结构

配置文件位置：`config/mqtt_config.json`

```json
{
  "mqtt_server": {
    "host": "*************",
    "port": 1883
  },
  "channels": {
    "C0": {
      "clientId": "S&D19QDKG1WYPE6&188&19",
      "username": "bydq_admin",
      "passwd": "PUFCI42X54B45PQ5",
      "subscribeTopic": "/188/D19QDKG1WYPE6/function/get",
      "reportTopic": "/188/D19QDKG1WYPE6/property/post",
      "data_files": ["data/c0_parsed_data.json", "data/c5_parsed_data.json"],
      "publish_interval": 30
    },
    "59": {
      "clientId": "S&D19EOEN59V1MJ&197&19",
      "username": "bydq_admin",
      "passwd": "P5OGL9C1V3E7W06P",
      "subscribeTopic": "/197/D19EOEN59V1MJ/function/get",
      "reportTopic": "/197/D19EOEN59V1MJ/property/post",
      "data_files": ["data/59_parsed_data.json"],
      "publish_interval": 30
    }
    // ... 其他通道配置
  }
}
```

## 使用方法

### 1. 启动单个通道

```bash
# 启动C0通道
python src/services/mqtt_publisher.py C0

# 启动59通道
python src/services/mqtt_publisher.py 59

# 启动D6通道
python src/services/mqtt_publisher.py D6

# 启动D7通道
python src/services/mqtt_publisher.py D7

# 启动D8通道
python src/services/mqtt_publisher.py D8
```

### 2. 启动所有通道

```bash
# 同时启动所有通道
python src/services/mqtt_publisher.py ALL
```

### 3. 查看可用通道

```bash
# 查看帮助信息（会显示可用通道）
python src/services/mqtt_publisher.py
```

## 配置说明

### 服务器配置
- `host`: MQTT服务器地址
- `port`: MQTT服务器端口（默认1883）

### 通道配置
每个通道包含以下配置项：
- `clientId`: MQTT客户端ID
- `username`: MQTT用户名
- `passwd`: MQTT密码
- `subscribeTopic`: 订阅主题（接收控制命令）
- `reportTopic`: 发布主题（发送数据）
- `data_files`: 数据文件列表
- `publish_interval`: 发布间隔（秒）

### 最新更新
- **C5、D6、D7、D8通道配置已更新**：使用统一的MQTT配置信息
- **智能主题路由**：C0通道发布C5数据时自动使用C5通道的发布主题
- **配置统一管理**：所有通道配置集中在一个文件中管理

## 功能特性

1. **统一配置管理**：所有通道配置集中在一个文件中
2. **灵活启动方式**：支持单通道或多通道启动
3. **自动重连机制**：网络断开时自动重连
4. **数据定期发布**：按配置间隔自动发布数据
5. **错误处理**：完善的错误处理和日志记录

## 日志信息

系统会输出详细的日志信息，包括：
- 配置加载状态
- MQTT连接状态
- 数据发布状态
- 错误信息

## 注意事项

1. 确保配置文件 `config/mqtt_config.json` 存在且格式正确
2. 确保数据文件存在（如 `data/c0_parsed_data.json`）
3. 网络连接正常，能够访问MQTT服务器
4. 使用 Ctrl+C 安全停止服务

## 故障排除

### 连接问题

#### 连接失败
- 检查网络连接状态
- 验证MQTT服务器地址和端口
- 确认用户名和密码正确
- 检查clientId是否冲突
- 查看防火墙设置

#### 连接断开
- 检查网络稳定性
- 查看MQTT服务器状态
- 验证keep-alive设置
- 检查是否有重复的clientId连接

### 数据发布问题

#### 发布失败
- 检查数据文件是否存在
- 验证数据文件JSON格式
- 确认发布主题权限
- 检查QoS设置
- 验证消息大小限制

#### 数据不更新
- 检查定期发布定时器状态
- 验证数据文件修改时间
- 确认发布间隔设置
- 检查线程运行状态

### 配置问题

#### 配置加载失败
- 检查配置文件路径（相对路径：`../../config/mqtt_config.json`）
- 验证JSON格式是否正确
- 确认文件权限和编码（UTF-8）
- 检查配置文件完整性

#### 通道配置错误
- 验证通道名称拼写
- 检查必需字段是否完整
- 确认data_files路径正确
- 验证publish_interval数值

### 59通道特殊问题

#### 控制消息无效
- 检查消息JSON格式
- 验证id字段是否存在于数据文件中
- 确认subscribeTopic配置正确
- 检查消息编码（UTF-8）

#### writeValue更新失败
- 检查59_parsed_data.json文件权限
- 验证数据文件结构
- 确认id字段匹配
- 检查文件锁定状态

### 性能问题

#### 内存占用过高
- 检查定时器是否正确清理
- 验证线程数量
- 确认数据文件大小
- 检查日志级别设置

#### CPU占用过高
- 检查发布间隔设置
- 验证健康检查频率
- 确认重连机制是否过于频繁
- 检查主循环休眠时间

### 调试建议

1. **启用详细日志**：设置日志级别为DEBUG
2. **单通道测试**：先测试单个通道，再测试多通道
3. **网络工具**：使用MQTT客户端工具验证连接
4. **文件监控**：使用文件监控工具检查数据文件变化
5. **资源监控**：使用系统监控工具检查资源使用情况

## 示例配置

### 完整配置示例

```json
{
  "mqtt_server": {
    "host": "*************",
    "port": 1883
  },
  "channels": {
    "C0": {
      "clientId": "S&D19QDKG1WYPE6&188&19",
      "username": "bydq_admin",
      "passwd": "PUFCI42X54B45PQ5",
      "subscribeTopic": "/188/D19QDKG1WYPE6/function/get",
      "reportTopic": "/188/D19QDKG1WYPE6/property/post",
      "data_files": ["../../data/c0_parsed_data.json", "../../data/c5_parsed_data.json"],
      "publish_interval": 30
    },
    "59": {
      "clientId": "S&D19EOEN59V1MJ&197&19",
      "username": "bydq_admin",
      "passwd": "P5OGL9C1V3E7W06P",
      "subscribeTopic": "/197/D19EOEN59V1MJ/function/get",
      "reportTopic": "/197/D19EOEN59V1MJ/property/post",
      "data_files": ["../../data/59_parsed_data.json"],
      "publish_interval": 30
    },
    "C5_D6_D7_D8": {
      "clientId": "S&D19QDKG1WYPE6&188&19_VERSION",
      "username": "bydq_admin",
      "passwd": "PUFCI42X54B45PQ5",
      "subscribeTopic": "/188/D19QDKG1WYPE6/function/get",
      "reportTopic": "/188/D19QDKG1WYPE6/property/post",
      "data_files": [
        "../../data/c5_parsed_data.json",
        "../../data/d6_parsed_data.json",
        "../../data/d7_parsed_data.json",
        "../../data/d8_parsed_data.json"
      ],
      "publish_interval": 30
    }
  }
}
```

### 59通道控制消息示例

#### 单个参数更新
```json
{
  "id": "SVG_1001",
  "value": "2.5"
}
```

#### 批量参数更新
```json
[
  {
    "id": "SVG_1001",
    "value": "2.5"
  },
  {
    "id": "SVG_1002",
    "value": "3.0"
  }
]
```

## 版本历史

### v2.0.0 (当前版本)
- 新增多通道架构支持
- 新增UniversalMQTTPublisher通用发布器
- 新增59通道双向通信功能
- 优化配置管理和错误处理
- 新增健康检查和自动恢复机制

### v1.0.0
- 基础MQTT发布功能
- 统一配置管理
- 基本的重连机制