# Linux Python串口通信后台服务配置文件
# 配置格式: TOML

[service]
# 服务基本配置
name = "SerialPort Communication Service"
version = "1.0.0"
debug = true

[serial]
# 串口通信配置
port = "/dev/ttyUSB0"
baudrate = 19200
bytesize = 8
parity = "O"
stopbits = 1
timeout = 2.0
write_timeout = 1.0
xonxoff = false
rtscts = false
dsrdtr = false

# 连接重试配置
max_reconnect_attempts = 3
reconnect_delay = 1.0
connection_check_interval = 30.0

[protocol]
# 协议配置
frame_header = "EB,90,01,01"
frame_tail = "AA,AB"
address = 0x01
control_code = 0x03

# 命令超时配置
command_timeout = 2.0
response_timeout = 2.0
max_retries = 2

[scheduler]
# 任务调度配置
loop_interval = 1.0
manual_priority = true

# 初始化命令序列
init_commands = [
    "C0",
    "C5",
    "D6",
    "D7",
    "D8"
]

# 循环命令序列
loop_commands = [
    "C0", "C1", "C3",
    "C4", "C5",
    "D0", "D1", "D2",
    "D3", "D4", "D5",
    "D6", "D7", "D8"
]

[data]
# 数据处理配置
storage_path = "data"
max_cache_size = 1000

# 分类存储配置
[data.storage]
# 启用分类存储
enable_categorized_storage = true
# 存储策略: "command_based" (按命令分类), "time_based" (按时间分类), "mixed" (混合分类)
storage_strategy = "mixed"
# 数据保留天数
retention_days = 30
# 单个文件最大大小 (MB)
max_file_size = 10
# 压缩旧文件
compress_old_files = true

# 命令分类配置
[data.storage.categories]
# 系统状态类命令
system_status = ["C0", "C1", "C4", "C5"]
# 单元数据类命令
unit_data = ["D0", "D1", "D2", "D3", "D4", "D5", "D6", "D7", "D8"]
# 调试参数类命令
debug_data = ["59"]
# 水冷系统类命令
water_cooling = ["63", "64", "65"]
# 其他命令
other = []

# 自动保存配置
[data.auto_save]
enabled = true
interval = 60
save_on_warning = true
save_on_error = false
# 分类保存
categorized_save = true
# 批量保存阈值
batch_save_threshold = 50

# 数据格式配置
timestamp_format = "%Y-%m-%d %H:%M:%S"
decimal_places = 2

# 数据输出格式配置
# 支持的格式: "hex" (十六进制), "decimal" (十进制), "auto" (自动检测)
data_format = "hex"
# 是否保持原始十六进制格式
keep_hex_format = true
# 十六进制字符串是否使用大写
hex_uppercase = true

[logging]
# 日志配置
level = "INFO"
file = "logs/service.log"
format = "%(asctime)s [%(levelname)s] %(message)s"
max_size = "10MB"
backup_count = 3

# 控制台输出
console_output = true
console_level = "WARNING"

# 静默模式（生产环境使用）
quiet_mode = false
# 性能日志开关
performance_logging = false

[monitoring]
# 监控配置
enabled = true
check_interval = 30
metrics_history_size = 100

# 告警阈值
cpu_threshold = 80
memory_threshold = 85
disk_threshold = 90

[security]
# 安全配置
enable_authentication = false
api_key = ""
allowed_ips = ["127.0.0.1", "::1"]

[performance]
# 性能配置
max_concurrent_commands = 5
buffer_size = 4096
thread_pool_size = 4

# 资源限制
max_memory_usage = "512MB"
max_cpu_usage = 80