#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C4DSP状态协议解析器
用于解析C4协议返回的DSP状态数据，包含29个模拟量参数
支持IEEE754浮点数解析和参数映射
"""

import json
import struct
from datetime import datetime
from typing import Dict, Any, List, Optional


class C4DataParser:
    """
    C4DSP状态协议解析器
    解析C4协议返回的48个32位IEEE754浮点数，映射为38个DSP状态参数
    """
    
    def __init__(self):
        """
        初始化C4数据解析器
        设置参数映射表，将数据地址映射到物模型中的id和name
        """
        # C4协议参数映射表 - 根据协议文档和物模型文件定义
        # 数据地址(十进制) -> (物模型ID, 参数名称, 单位)
        self.parameter_mapping = {
            65: ("HMI_30065", "AB线电压", "V"),
            67: ("HMI_30067", "BC线电压", "V"), 
            69: ("HMI_30069", "CA线电压", "V"),
            71: ("HMI_30071", "电压Ud", "V"),
            73: ("HMI_30073", "电压Uq", "V"),
            75: ("HMI_30075", "A相SVG电流有效值", "A"),
            77: ("HMI_30077", "B相SVG电流有效值", "A"),
            79: ("HMI_30079", "C相SVG电流有效值", "A"),
            81: ("HMI_30081", "SVG输出有功", "kW"),
            83: ("HMI_30083", "SVG输出无功电流", "A"),
            85: ("HMI_30085", "负载有功电流", "A"),
            87: ("HMI_30087", "负载无功电流", "A"),
            89: ("HMI_30089", "网侧电压幅度", "V"),
            91: ("HMI_30091", "高压侧电压幅度", "V"),
            93: ("HMI_30093", "上级母线电压Ud", "V"),
            95: ("HMI_30095", "上级母线电压Uq", "V"),
            97: ("HMI_30097", "A相单元平均电压", "V"),
            99: ("HMI_30099", "B相单元平均电压", "V"),
            101: ("HMI_30101", "C相单元平均电压", "V"),
            103: ("HMI_30103", "三相单元平均电压", "V"),
            105: ("HMI_30105", "Id电流环PI调节器积分输出", "-"),
            107: ("HMI_30107", "Iq电流环PI调节器积分输出", "-"),
            109: ("HMI_30109", "主控CPU时间利用率", "%"),
            111: ("HMI_30111", "故障自动启动次数", "次"),
            113: ("HMI_30113", "远程SVG输出无功", "kVar"),
            115: ("HMI_30115", "电压无功模式电压调节器输出", "-"),
            117: ("HMI_30117", "负序电压d轴分量", "V"),
            119: ("HMI_30119", "负序电压q轴分量", "V"),
            121: ("HMI_30121", "负序电流d轴分量", "A"),
            123: ("HMI_30123", "负序电流q轴分量", "A"),
            125: ("HMI_30125", "谐波电流PI调节器d轴分量", "-"),
            127: ("HMI_30127", "谐波电流PI调节器q轴分量", "-"),
            129: ("HMI_30129", "电压调节器反馈值", "V"),
            131: ("HMI_30131", "电压调节器参考值", "V"),
            133: ("HMI_30133", "A相SVG电流有效值CT采样", "A"),
            135: ("HMI_30135", "B相SVG电流有效值CT采样", "A"),
            137: ("HMI_30137", "C相SVG电流有效值CT采样", "A"),
            139: ("HMI_30139", "SVG零序电流有效值CT采样", "A")
        }
    
    def extract_data_fields(self, raw_hex: str) -> Dict[str, Any]:
        """
        从C4协议原始十六进制数据中提取数据字段
        验证协议格式并提取48个32位浮点数数据
        
        Args:
            raw_hex: C4协议返回的原始十六进制字符串
            
        Returns:
            dict: 包含data_hex和data_hex_formatted的字典
        """
        try:
            # 移除空格并转换为字节数组
            clean_hex = raw_hex.replace(" ", "")
            if len(clean_hex) % 2 != 0:
                raise ValueError("十六进制字符串长度必须为偶数")

            data = bytes.fromhex(clean_hex)

            # 验证最小长度（帧头+地址+功能码+命令码+数据长度+数据+校验+帧尾）
            if len(data) < 9:
                raise ValueError(f"数据长度不足，至少需要9字节，实际为{len(data)}字节")

            # 验证帧头帧尾
            if data[0:4] != b'\xEB\x90\x01\x01':
                raise ValueError("无效的帧头")
            if data[-2:] != b'\xAA\xAB':
                raise ValueError("无效的帧尾")

            # 验证命令码
            if data[4] != 0xC4:
                raise ValueError(f"命令码不匹配，期望0xC4，实际为0x{data[4]:02X}")

            # 获取数据长度
            data_length = data[5]
            if data_length != 0xC0:  # 192字节 (48个32位浮点数)
                raise ValueError(f"数据长度不匹配，期望0xC0，实际为0x{data_length:02X}")

            # 根据C4协议提取数据部分
            # C4命令的数据从第9字节开始（帧头4 + 命令1 + 数据长度1 + 空字节占位3 = 9）
            data_start = 9
            data_end = data_start + data_length
            data_bytes = data[data_start:data_end]

            # 生成data_hex字段（原始十六进制数据）
            data_hex = data_bytes.hex().upper()

            # 生成data_hex_formatted字段（按4字节一组分为32位IEEE754浮点数）
            data_hex_formatted = []
            if len(data_bytes) % 4 == 0:
                # C4命令：按4字节一组分为32位IEEE754浮点数（小端序）
                for i in range(0, len(data_bytes), 4):
                    # 提取4字节并转换为十六进制字符串
                    float_bytes = data_bytes[i:i+4]
                    hex_str = float_bytes.hex().upper()
                    data_hex_formatted.append(f'0x{hex_str}')
            else:
                # 如果字节数不是4的倍数，按字节显示
                data_hex_formatted = [f'0x{b:02X}' for b in data_bytes]

            return {
                'data_hex': data_hex,
                'data_hex_formatted': data_hex_formatted,
                'success': True
            }

        except Exception as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': str(e)
            }

    def parse_ieee754_float(self, hex_bytes: bytes) -> float:
        """
        解析IEEE754格式的32位浮点数（小端序）

        Args:
            hex_bytes: 4字节的十六进制数据

        Returns:
            float: 解析后的浮点数值
        """
        try:
            # 使用struct解析IEEE754浮点数（小端序）
            value = struct.unpack('<f', hex_bytes)[0]
            return value
        except Exception as e:
            raise ValueError(f"IEEE754浮点数解析失败: {e}")

    def parse_dsp_parameters_from_data(self, data_bytes: bytes) -> Dict[str, Any]:
        """
        从C4协议数据字节中解析DSP状态参数
        按照协议文档中的参数映射表解析48个32位浮点数

        Args:
            data_bytes: C4协议数据区域的字节数据（192字节）

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            if len(data_bytes) != 192:  # 48个32位浮点数 = 192字节
                raise ValueError(f"数据长度不正确，期望192字节，实际为{len(data_bytes)}字节")

            parameters = []

            # 解析48个32位浮点数，但只有38个有效参数
            for i in range(48):  # 48个32位浮点数
                # 计算当前浮点数的字节偏移
                offset = i * 4
                float_bytes = data_bytes[offset:offset+4]

                # 计算对应的数据地址（从65开始，每个地址间隔2）
                data_address = 65 + (i * 2)

                # 检查该地址是否在参数映射表中
                if data_address in self.parameter_mapping:
                    try:
                        # 解析IEEE754浮点数
                        float_value = self.parse_ieee754_float(float_bytes)

                        # 获取参数信息
                        param_id, param_name, param_unit = self.parameter_mapping[data_address]

                        # 构建参数信息
                        param_info = {
                            'address': data_address,
                            'id': param_id,
                            'name': param_name,
                            'unit': param_unit,
                            'value': float_value,
                            'raw_bytes': float_bytes.hex().upper(),
                            'index': i
                        }

                        parameters.append(param_info)

                    except Exception as e:
                        # 单个参数解析失败时，记录错误但继续处理其他参数
                        param_id, param_name, param_unit = self.parameter_mapping[data_address]
                        param_info = {
                            'address': data_address,
                            'id': param_id,
                            'name': param_name,
                            'unit': param_unit,
                            'value': 0.0,
                            'raw_bytes': float_bytes.hex().upper(),
                            'index': i,
                            'error': str(e)
                        }
                        parameters.append(param_info)

            return {
                'command': 'C4',
                'parameters': parameters,
                'total_floats': 48,
                'valid_parameters': len([p for p in parameters if 'error' not in p]),
                'error_parameters': len([p for p in parameters if 'error' in p]),
                'success': True
            }

        except Exception as e:
            return {
                'command': 'C4',
                'parameters': [],
                'success': False,
                'error': str(e)
            }

    def parse_dsp_response(self, raw_hex: str) -> Dict[str, Any]:
        """
        解析C4协议的DSP状态响应数据
        整合数据提取和参数解析功能

        Args:
            raw_hex: 原始十六进制字符串

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            # 第一步：提取data_hex和data_hex_formatted字段
            extract_result = self.extract_data_fields(raw_hex)
            if not extract_result['success']:
                return {
                    'command': 'C4',
                    'parameters': [],
                    'raw_data': raw_hex,
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': f"数据提取失败: {extract_result.get('error', '未知错误')}"
                }

            # 第二步：从原始数据中提取数据字节进行参数解析
            clean_hex = raw_hex.replace(" ", "")
            data = bytes.fromhex(clean_hex)
            data_start = 9
            data_length = data[5]  # 应该是0xC0 (192字节)
            data_bytes = data[data_start:data_start + data_length]

            # 第三步：基于数据字节进行DSP参数解析
            parse_result = self.parse_dsp_parameters_from_data(data_bytes)
            if not parse_result['success']:
                return {
                    'command': 'C4',
                    'parameters': [],
                    'raw_data': raw_hex,
                    'data_hex': extract_result['data_hex'],
                    'data_hex_formatted': extract_result['data_hex_formatted'],
                    'success': False,
                    'error': f"参数解析失败: {parse_result.get('error', '未知错误')}"
                }

            # 合并结果
            result = parse_result.copy()
            result.update({
                'raw_data': raw_hex,
                'data_hex': extract_result['data_hex'],
                'data_hex_formatted': extract_result['data_hex_formatted']
            })

            return result

        except Exception as e:
            return {
                'command': 'C4',
                'parameters': [],
                'raw_data': raw_hex,
                'success': False,
                'error': f"解析过程异常: {str(e)}"
            }

    def parse_c4_data(self, raw_hex: str, timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        解析C4命令的raw_hex数据
        整合数据提取和参数解析功能，生成MQTT消息格式

        Args:
            raw_hex: C4命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间

        Returns:
            包含C4DSP状态参数的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        result = []

        if not raw_hex:
            raise ValueError("C4命令数据格式错误，数据不能为空")

        try:
            # 解析数据（使用整合方法）
            parsed_data = self.parse_dsp_response(raw_hex)

            # 检查解析是否成功
            if not parsed_data.get('success', False):
                error_msg = parsed_data.get('error', '未知解析错误')
                raise ValueError(f"C4命令数据解析失败: {error_msg}")

            # 生成MQTT消息格式
            for param_info in parsed_data.get('parameters', []):
                # 构建基本信息 - 格式化value为4位小数，移除unit字段
                param_data = {
                    "id": param_info["id"],
                    "name": param_info["name"],
                    "ts": timestamp,
                    "value": round(float(param_info['value']), 4)  # 四舍五入到4位小数
                }

                # 添加额外的调试信息（可选）
                if param_info.get('raw_bytes'):
                    param_data['raw_bytes'] = param_info['raw_bytes']
                if param_info.get('error'):
                    param_data['parse_error'] = param_info['error']

                result.append(param_data)

            return result

        except Exception as e:
            # 增强错误处理，提供更详细的错误信息
            raise ValueError(f"C4命令数据解析过程异常: {str(e)}")

    def parse_c4_data_to_json(self, raw_hex: str, timestamp: Optional[str] = None,
                            output_file: Optional[str] = None, pretty_print: bool = True) -> str:
        """
        解析C4命令数据并输出为JSON字符串或保存到文件

        Args:
            raw_hex: C4命令返回的原始十六进制字符串
            timestamp: 时间戳
            output_file: 输出文件路径，如果不提供则返回JSON字符串
            pretty_print: 是否格式化输出JSON

        Returns:
            JSON字符串
        """
        parsed_data = self.parse_c4_data(raw_hex, timestamp)

        if pretty_print:
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)
        else:
            json_str = json.dumps(parsed_data, ensure_ascii=False)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"解析结果已保存到: {output_file}")

        return json_str


def main():
    """
    主函数
    从realtime_data.json读取C4命令数据并进行解析
    """
    print("=== C4DSP状态协议解析器 ===")

    realtime_data_file = "data/realtime_data.json"
    parser = C4DataParser()

    try:
        # 读取realtime_data.json
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"成功读取 {realtime_data_file}")
        print(f"数据项总数: {len(data)}")
        print()

        # 查找C4命令数据
        c4_data_found = False
        for item in data:
            if item.get('command') == 'C4' and item.get('success'):
                c4_data_found = True

                print(f"找到C4命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")
                print()

                # 使用raw_hex进行完整解析
                raw_hex = item.get('raw_hex', '')
                if not raw_hex:
                    print("警告: raw_hex数据为空，跳过解析")
                    continue

                # 步骤1: 数据提取验证
                print("步骤1: 验证协议格式并提取数据")
                extract_result = parser.extract_data_fields(raw_hex)
                if extract_result['success']:
                    print(f"✓ 协议验证通过")
                    print(f"✓ 数据长度: {len(extract_result['data_hex'])//2} 字节")
                    print(f"✓ 浮点数个数: {len(extract_result['data_hex_formatted'])} 个")
                else:
                    print(f"✗ 协议验证失败: {extract_result['error']}")
                    continue
                print()

                # 步骤2: DSP参数解析
                print("步骤2: 解析DSP状态参数")
                dsp_result = parser.parse_dsp_response(raw_hex)
                if dsp_result['success']:
                    print(f"✓ DSP参数解析成功")
                    print(f"✓ 有效参数: {dsp_result['valid_parameters']} 个")
                    print(f"✓ 错误参数: {dsp_result['error_parameters']} 个")

                    # 显示前几个参数作为示例
                    print("前5个参数示例:")
                    for i, param in enumerate(dsp_result['parameters'][:5]):
                        print(f"  {param['name']}: {param['value']:.6f} {param['unit']}")
                else:
                    print(f"✗ DSP参数解析失败: {dsp_result['error']}")
                    continue
                print()

                # 步骤3: 生成最终的MQTT格式数据
                print("步骤3: 生成MQTT格式数据")
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

                # 调用parse_c4_data_to_json，同时完成显示和保存
                output_file = "data/c4_parsed_data.json"
                json_result = parser.parse_c4_data_to_json(
                    raw_hex,
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                print("MQTT格式解析结果:")
                print(json_result)

                break  # 只处理第一个找到的C4数据

        if not c4_data_found:
            print("在realtime_data.json中未找到有效的C4命令数据")
            print("请确保文件中包含command='C4'且success=true的数据项")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()
