# D6-D7-D8单元程序版本信息协议解析文档

## 1. 命令概述

D6、D7、D8命令用于查询A相、B相、C相的单元程序版本信息，分别对应三个相位。每个相位包含12个单元的版本信息，每个单元占用2字节。

- **D6 (0xD6)**: 查询A相单元程序版本信息
- **D7 (0xD7)**: 查询B相单元程序版本信息  
- **D8 (0xD8)**: 查询C相单元程序版本信息

## 2. 协议格式

### 2.1 查询命令格式

查询命令为固定格式的16字节帧：

| 字节位置 | 字段名称 | 长度 | 说明 |
|----------|----------|------|------|
| 0-3      | 帧头     | 4字节 | 固定值：EB 90 01 01 |
| 4        | 命令码   | 1字节 | D6/D7/D8 |
| 5        | 数据长度 | 1字节 | 固定值：00 |
| 6-13     | 保留字节 | 8字节 | 固定值：00 00 00 00 00 00 00 00 |
| 14       | 校验和   | 1字节 | 单字节和校验 |
| 15-16    | 帧尾     | 2字节 | 固定值：AA AB |

### 2.2 响应帧格式

响应帧为48字节，格式如下：

| 字节位置 | 字段名称 | 长度 | 说明 |
|----------|----------|------|------|
| 0-3      | 帧头     | 4字节 | 固定值：EB 90 01 01 |
| 4        | 命令码   | 1字节 | D6/D7/D8 |
| 5        | 数据长度 | 1字节 | 固定值：24 (0x24) |
| 6-8      | 保留字节 | 3字节 | 固定值：00 00 00 |
| 9-32     | 版本数据 | 24字节 | 12单元×2字节版本信息 |
| 33-44    | 保留字节 | 12字节 | 固定值：00... |
| 45       | 校验和   | 1字节 | 单字节和校验 |
| 46-47    | 帧尾     | 2字节 | 固定值：AA AB |

## 3. 版本数据结构

### 3.1 单元版本信息格式

每个单元占用2字节，格式如下：

| 字节位置 | 字段名称 | 说明 |
|----------|----------|------|
| 第1字节  | 低字节   | 小头排列的低8位 |
| 第2字节  | 高字节   | 小头排列的高8位 |

**数据解析**：
- 将2字节按小头排列组合为16位数值（0x高字节低字节）
- 将16位数值按BCD码解析为4个数字，每4位代表一个数字
- 从左到右依次为主版本号、次版本号（2位数字）、修订号

### 3.2 版本号解析规则

- **格式**：`主版本号.次版本号.修订号`
- **解析方法**：
  1. 将两个字节按小头排列（低字节在前，高字节在后）组合为16位数值
  2. 将16位数值按BCD码解析，每4位代表一个数字
  3. 从左到右依次为主版本号、次版本号（2位数字）、修订号
- **示例**：
  - 数据 `24 41` → 小头排列为 `0x4124` → BCD码解析为 `4.12.4`
  - 数据 `00 00` → 单元被旁路
  - 数据 `35 27` → 小头排列为 `0x2735` → BCD码解析为 `2.73.5`

### 3.3 旁路状态

当单元数据为 `00 00` 时，表示该单元被旁路，不参与工作。

## 4. 实际数据解析示例

### 4.1 完整响应帧示例

以A相响应数据为例：

```
原始数据：
EB 90 01 01 D6 24 00 00 24 41 24 41 24 41 24 41
24 41 24 41 24 41 24 41 24 41 24 41 00 00 00 00
00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
00 00 00 00 69 AA AB
```

### 4.2 版本数据分解

版本数据部分（字节8-31）：

| 单元序号 | 原始数据 | 版本号 | 状态 |
|----------|----------|--------|------|
| 第1单元  | 24 41    | 4.12.4 | 激活 |
| 第2单元  | 24 41    | 4.12.4 | 激活 |
| 第3单元  | 24 41    | 4.12.4 | 激活 |
| 第4单元  | 24 41    | 4.12.4 | 激活 |
| 第5单元  | 24 41    | 4.12.4 | 激活 |
| 第6单元  | 24 41    | 4.12.4 | 激活 |
| 第7单元  | 24 41    | 4.12.4 | 激活 |
| 第8单元  | 24 41    | 4.12.4 | 激活 |
| 第9单元  | 24 41    | 4.12.4 | 激活 |
| 第10单元 | 24 41    | 4.12.4 | 激活 |
| 第11单元 | 00 00    | -      | 旁路 |
| 第12单元 | 00 00    | -      | 旁路 |

### 4.3 解析结果

```
单元程序版本信息解析结果 - A相
================================================================================
命令码: 0xD6
数据长度: 0x24 (36字节)
校验和验证: 通过
激活单元数: 10/12

单元版本信息:
--------------------------------------------------------------------------------
单元序号   版本号         状态     原始数据
--------------------------------------------------------------------------------
第1单元    4.12.4        激活     24 41
第2单元    4.12.4        激活     24 41
第3单元    4.12.4        激活     24 41
第4单元    4.12.4        激活     24 41
第5单元    4.12.4        激活     24 41
第6单元    4.12.4        激活     24 41
第7单元    4.12.4        激活     24 41
第8单元    4.12.4        激活     24 41
第9单元    4.12.4        激活     24 41
第10单元   4.12.4        激活     24 41
第11单元   -             旁路     00 00
第12单元   -             旁路     00 00
```

## 5. Python解析代码示例

### 5.1 完整解析代码

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
from typing import Dict, List, Any

class UnitVersionParser:
    """单元程序版本信息解析器"""
    
    def __init__(self):
        self.phase_commands = {0xD6: "A相", 0xD7: "B相", 0xD8: "C相"}
    
    def parse_unit_version_info(self, data: bytes) -> Dict[str, Dict[str, Any]]:
        """解析单元程序版本信息数据"""
        if len(data) != 24:
            raise ValueError(f"数据长度必须为24字节，实际为{len(data)}字节")
        
        result = {}
        for unit_index in range(12):
            start_pos = unit_index * 2
            unit_data = data[start_pos:start_pos + 2]
            
            unit_number = unit_index + 1
            unit_key = f"第{unit_number}单元"
            
            if unit_data == b'\x00\x00':
                version_str = "旁路"
                raw_hex = "00 00"
                is_active = False
            else:
                # 小头排列：低字节在前，高字节在后
                low_byte, high_byte = unit_data[0], unit_data[1]
                
                # 组合为16位数值（0x高字节低字节）
                combined = (high_byte << 8) | low_byte
                
                # 按BCD码解析，每4位代表一个数字
                # 0x4124 -> 4, 12, 4
                major = (combined >> 12) & 0x0F      # 第1个BCD数字（千位）
                minor_tens = (combined >> 8) & 0x0F  # 第2个BCD数字（百位）
                minor_ones = (combined >> 4) & 0x0F  # 第3个BCD数字（十位）
                patch = combined & 0x0F              # 第4个BCD数字（个位）
                
                # 组合次版本号：百位和十位组合成12
                minor = minor_tens * 10 + minor_ones
                
                version_str = f"{major}.{minor}.{patch}"
                raw_hex = f"{low_byte:02X} {high_byte:02X}"
                is_active = True
            
            result[unit_key] = {
                'raw_bytes': raw_hex,
                'version': version_str,
                'status': '激活' if is_active else '旁路',
                'is_active': is_active,
                'unit_number': unit_number
            }
        
        return result
    
    def parse_d6_d7_d8_response(self, response_data: bytes) -> Dict[str, Any]:
        """解析D6/D7/D8响应数据"""
        if len(response_data) != 48:
            raise ValueError(f"响应帧长度必须为48字节，实际为{len(response_data)}字节")
        
        # 验证帧头
        if response_data[0:4] != b'\xEB\x90\x01\x01':
            raise ValueError(f"无效的帧头: {response_data[0:4].hex().upper()}")
        
        # 提取命令码
        command = response_data[4]
        phase = self.phase_commands.get(command, f"未知命令(0x{command:02X})")
        
        # 验证数据长度
        data_length = response_data[5]
        if data_length != 0x24:
            raise ValueError(f"数据长度应为0x24，实际为0x{data_length:02X}")
        
        # 提取版本数据（字节8-31）
        version_data = response_data[8:32]
        
        # 解析版本信息
        version_info = self.parse_unit_version_info(version_data)
        
        # 统计激活单元数量
        active_units = [info for info in version_info.values() if info['is_active']]
        
        return {
            'phase': phase,
            'command': f"0x{command:02X}",
            'data_length': data_length,
            'total_units': 12,
            'active_units': len(active_units),
            'units': version_info,
            'raw_response': response_data.hex().upper()
        }

# 使用示例
if __name__ == "__main__":
    parser = UnitVersionParser()
    
    # 示例响应数据
    response_data = bytes.fromhex(
        "EB900101D624000000244124412441244124412441244124412441244124412441"
        "0000000000000000000000000000000000000000000000000000000000000000"
        "69AAAB"
    )
    
    result = parser.parse_d6_d7_d8_response(response_data)
    print(f"解析结果: {result}")
```

### 5.2 使用示例

```python
# 创建解析器实例
parser = UnitVersionParser()

# 解析A相数据
a_phase_data = bytes.fromhex("EB900101D624000000...")
result = parser.parse_d6_d7_d8_response(a_phase_data)

# 输出结果
print(f"相位: {result['phase']}")
print(f"激活单元数: {result['active_units']}")

for unit, info in result['units'].items():
    print(f"{unit}: 版本{info['version']}, 状态{info['status']}")
```

## 6. 校验和计算

### 6.1 计算方法

校验和采用单字节和校验方式：

1. 计算除校验位外的所有字节之和
2. 取结果的低8位作为校验值

### 6.2 Python实现

```python
def calculate_checksum(self, data: bytes) -> int:
    """计算校验和"""
    return sum(data) & 0xFF

def verify_checksum(self, frame_data: bytes) -> bool:
    """验证校验和"""
    received_checksum = frame_data[-3]
    calculated_checksum = self.calculate_checksum(frame_data[:-3])
    return received_checksum == calculated_checksum
```

## 7. 注意事项

### 7.1 数据长度要求

- 响应帧必须为48字节
- 版本数据部分必须为24字节（12单元×2字节）

### 7.2 版本号范围

- 主版本号：0-15
- 次版本号：0-15
- 修订号：0-15

### 7.3 错误处理

- 数据长度不符：抛出ValueError异常
- 帧头错误：抛出ValueError异常
- 校验和错误：记录警告信息
- 旁路单元：版本号显示为"旁路"

### 7.4 使用建议

1. 在实际应用中，建议先验证校验和
2. 对于旁路单元，可以跳过相关处理
3. 版本号可用于软件升级判断
4. 建议定期查询以监控单元状态