#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D0命令数据解析器
根据D0-D1-D2单元直流侧电压协议解析文档将十六进制数据转换为A相单元直流侧电压JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional


class D0DataParser:
    """
    D0命令数据解析器类
    用于解析D0命令返回的A相单元直流侧电压数据
    """
    
    def __init__(self):
        """
        初始化解析器，定义A相单元映射关系
        """
        # A相单元映射（根据物模型表）
        self.unit_mapping = {
            1: {"id": "HMI_30190", "name": "A相单元直流侧电压（A1）"},
            2: {"id": "HMI_30191", "name": "A相单元直流侧电压（A2）"},
            3: {"id": "HMI_30192", "name": "A相单元直流侧电压（A3）"},
            4: {"id": "HMI_30193", "name": "A相单元直流侧电压（A4）"},
            5: {"id": "HMI_30194", "name": "A相单元直流侧电压（A5）"},
            6: {"id": "HMI_30195", "name": "A相单元直流侧电压（A6）"},
            7: {"id": "HMI_30196", "name": "A相单元直流侧电压（A7）"},
            8: {"id": "HMI_30197", "name": "A相单元直流侧电压（A8）"},
            9: {"id": "HMI_30198", "name": "A相单元直流侧电压（A9）"},
            10: {"id": "HMI_30199", "name": "A相单元直流侧电压（A10）"},
            11: {"id": "HMI_30200", "name": "A相单元直流侧电压（A11）"},
            12: {"id": "HMI_30201", "name": "A相单元直流侧电压（A12）"}
        }
    
    def extract_data_fields(self, raw_hex: str) -> Dict[str, Any]:
        """
        从raw_hex数据中提取data_hex和data_hex_formatted字段
        参考simple_main.py中的处理逻辑

        Args:
            raw_hex: 原始十六进制字符串

        Returns:
            dict: 包含data_hex和data_hex_formatted的字典
        """
        try:
            # 移除空格并转换为字节数组
            clean_hex = raw_hex.replace(" ", "")
            if len(clean_hex) % 2 != 0:
                raise ValueError("十六进制字符串长度必须为偶数")

            data = bytes.fromhex(clean_hex)

            # 验证最小长度（帧头+地址+功能码+命令码+数据长度+数据+校验+帧尾）
            if len(data) < 9:
                raise ValueError(f"数据长度不足，至少需要9字节，实际为{len(data)}字节")

            # 验证帧头帧尾
            if data[0:4] != b'\xEB\x90\x01\x01':
                raise ValueError("无效的帧头")
            if data[-2:] != b'\xAA\xAB':
                raise ValueError("无效的帧尾")

            # 验证命令码
            if data[4] != 0xD0:
                raise ValueError(f"命令码不匹配，期望0xD0，实际为0x{data[4]:02X}")

            # 获取数据长度
            data_length = data[5]
            if data_length != 0x24:  # 36字节
                raise ValueError(f"数据长度不匹配，期望0x24，实际为0x{data_length:02X}")

            # 根据simple_main.py的逻辑提取数据部分
            # D0命令的数据从第9字节开始（帧头4 + 命令1 + 数据长度1 + 空字节占位3 = 9）
            data_start = 9
            data_end = data_start + data_length
            data_bytes = data[data_start:data_end]

            # 生成data_hex字段（原始十六进制数据）
            data_hex = data_bytes.hex().upper()

            # 生成data_hex_formatted字段（按2字节一组分为16位值，参考simple_main.py）
            data_hex_formatted = []
            if len(data_bytes) % 2 == 0:
                # D0命令：按2字节一组分为16位值（小端序）
                for i in range(0, len(data_bytes), 2):
                    value = int.from_bytes(data_bytes[i:i+2], byteorder='little')
                    data_hex_formatted.append(f'0x{value:04X}')
            else:
                # 如果字节数为奇数，按字节显示
                data_hex_formatted = [f'0x{b:02X}' for b in data_bytes]

            return {
                'data_hex': data_hex,
                'data_hex_formatted': data_hex_formatted,
                'success': True
            }

        except Exception as e:
            return {
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': str(e)
            }

    def parse_voltage_from_formatted_data(self, data_hex_formatted: List[str]) -> Dict[str, Any]:
        """
        基于data_hex_formatted数据进行电压解析处理
        根据D0-D1-D2单元直流侧电压协议解析文档进行解析

        Args:
            data_hex_formatted: 格式化后的十六进制数据列表

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            if not data_hex_formatted or len(data_hex_formatted) < 12:
                raise ValueError(f"数据不足，至少需要12个16位值，实际为{len(data_hex_formatted)}个")

            # 解析每个单元的电压（前12个单元为有效数据）
            voltages = []
            for i in range(min(12, len(data_hex_formatted))):
                try:
                    # 从格式化的十六进制字符串中提取数值
                    hex_str = data_hex_formatted[i]
                    if hex_str.startswith('0x') or hex_str.startswith('0X'):
                        value = int(hex_str, 16)
                    else:
                        value = int(hex_str, 16)

                    # 判断旁路状态（11-12单元数据为0表示旁路）
                    status = "旁路" if value == 0 and i >= 10 else "正常"

                    voltages.append({
                        'unit': i + 1,
                        'voltage': value,
                        'status': status,
                        'raw_value': value,
                        'hex_value': hex_str
                    })

                except (ValueError, IndexError) as e:
                    # 单个数据解析失败时，记录错误但继续处理其他数据
                    voltages.append({
                        'unit': i + 1,
                        'voltage': 0,
                        'status': '解析错误',
                        'raw_value': 0,
                        'hex_value': data_hex_formatted[i] if i < len(data_hex_formatted) else 'N/A',
                        'error': str(e)
                    })

            return {
                'phase': 'A',
                'command': 'D0',
                'voltages': voltages,
                'total_units': len(voltages),
                'valid_units': len([v for v in voltages if v['status'] == '正常']),
                'bypass_units': len([v for v in voltages if v['status'] == '旁路']),
                'error_units': len([v for v in voltages if v['status'] == '解析错误']),
                'success': True
            }

        except Exception as e:
            return {
                'phase': 'A',
                'command': 'D0',
                'voltages': [],
                'success': False,
                'error': str(e)
            }

    def parse_voltage_response(self, raw_hex: str) -> Dict[str, Any]:
        """
        解析D0协议的电压响应数据
        整合数据提取和电压解析功能

        Args:
            raw_hex: 原始十六进制字符串

        Returns:
            dict: 包含解析结果的字典
        """
        try:
            # 第一步：提取data_hex和data_hex_formatted字段
            extract_result = self.extract_data_fields(raw_hex)
            if not extract_result['success']:
                return {
                    'phase': 'A',
                    'command': 'D0',
                    'voltages': [],
                    'raw_data': raw_hex,
                    'data_hex': '',
                    'data_hex_formatted': [],
                    'success': False,
                    'error': f"数据提取失败: {extract_result.get('error', '未知错误')}"
                }

            # 第二步：基于data_hex_formatted进行电压解析
            parse_result = self.parse_voltage_from_formatted_data(extract_result['data_hex_formatted'])
            if not parse_result['success']:
                return {
                    'phase': 'A',
                    'command': 'D0',
                    'voltages': [],
                    'raw_data': raw_hex,
                    'data_hex': extract_result['data_hex'],
                    'data_hex_formatted': extract_result['data_hex_formatted'],
                    'success': False,
                    'error': f"电压解析失败: {parse_result.get('error', '未知错误')}"
                }

            # 合并结果
            result = parse_result.copy()
            result.update({
                'raw_data': raw_hex,
                'data_hex': extract_result['data_hex'],
                'data_hex_formatted': extract_result['data_hex_formatted']
            })

            return result

        except Exception as e:
            return {
                'phase': 'A',
                'command': 'D0',
                'voltages': [],
                'raw_data': raw_hex,
                'data_hex': '',
                'data_hex_formatted': [],
                'success': False,
                'error': f"解析过程异常: {str(e)}"
            }
    
    def parse_d0_data(self, raw_hex: str, timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        解析D0命令的raw_hex数据
        整合数据提取和电压解析功能，生成MQTT消息格式

        Args:
            raw_hex: D0命令返回的原始十六进制字符串
            timestamp: 时间戳，如果不提供则使用当前时间

        Returns:
            包含A相单元直流侧电压的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        result = []

        if not raw_hex:
            raise ValueError("D0命令数据格式错误，数据不能为空")

        try:
            # 解析数据（使用新的整合方法）
            parsed_data = self.parse_voltage_response(raw_hex)

            # 检查解析是否成功
            if not parsed_data.get('success', False):
                error_msg = parsed_data.get('error', '未知解析错误')
                raise ValueError(f"D0命令数据解析失败: {error_msg}")

            # 生成MQTT消息格式
            for voltage_info in parsed_data.get('voltages', []):
                unit_num = voltage_info['unit']
                if unit_num in self.unit_mapping:
                    unit_config = self.unit_mapping[unit_num]

                    # 构建基本信息
                    unit_data = {
                        "id": unit_config["id"],
                        "name": unit_config["name"],
                        "ts": timestamp,
                        "value": str(voltage_info['voltage'])  # 转换为字符串以保持一致性
                    }

                    # 添加额外的调试信息（可选）
                    if voltage_info.get('status') != '正常':
                        unit_data['status'] = voltage_info['status']
                    if voltage_info.get('hex_value'):
                        unit_data['hex_value'] = voltage_info['hex_value']
                    if voltage_info.get('error'):
                        unit_data['parse_error'] = voltage_info['error']

                    result.append(unit_data)

            return result

        except Exception as e:
            # 增强错误处理，提供更详细的错误信息
            raise ValueError(f"D0命令数据解析过程异常: {str(e)}")
    
    def parse_d0_data_to_json(self, raw_hex: str, timestamp: Optional[str] = None,
                            output_file: Optional[str] = None, pretty_print: bool = True) -> str:
        """
        解析D0命令数据并输出为JSON字符串或保存到文件
        
        Args:
            raw_hex: D0命令返回的原始十六进制字符串
            timestamp: 时间戳
            output_file: 输出文件路径，如果不提供则返回JSON字符串
            pretty_print: 是否格式化输出JSON
            
        Returns:
            JSON字符串
        """
        parsed_data = self.parse_d0_data(raw_hex, timestamp)
        
        if pretty_print:
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)
        else:
            json_str = json.dumps(parsed_data, ensure_ascii=False)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"解析结果已保存到: {output_file}")
        
        return json_str


def main():
    """
    主函数，从realtime_data.json文件中读取D0命令数据并解析
    演示新的数据提取和解析功能
    """
    parser = D0DataParser()

    # 读取realtime_data.json文件
    realtime_data_file = "data/realtime_data.json"

    try:
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("D0命令数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)

        # 查找D0命令数据
        d0_data_found = False
        for item in data:
            if item.get('command') == 'D0' and item.get('success'):
                d0_data_found = True

                print(f"找到D0命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")

                # 如果存在data_hex_formatted，显示它
                if item.get('data_hex_formatted'):
                    print(f"已有格式化数据: {item.get('data_hex_formatted')}")

                print()

                # 使用raw_hex进行完整解析（包含数据提取和电压解析）
                raw_hex = item.get('raw_hex', '')
                if not raw_hex:
                    print("警告: raw_hex数据为空，跳过解析")
                    continue

                # 演示数据提取功能
                print("步骤1: 数据提取")
                extract_result = parser.extract_data_fields(raw_hex)
                if extract_result['success']:
                    print(f"  data_hex: {extract_result['data_hex']}")
                    print(f"  data_hex_formatted: {extract_result['data_hex_formatted']}")
                else:
                    print(f"  数据提取失败: {extract_result.get('error')}")
                    continue

                print()

                # 演示基于格式化数据的电压解析
                print("步骤2: 电压解析")
                voltage_result = parser.parse_voltage_from_formatted_data(extract_result['data_hex_formatted'])
                if voltage_result['success']:
                    print(f"  解析成功，共{voltage_result['total_units']}个单元")
                    print(f"  正常单元: {voltage_result['valid_units']}个")
                    print(f"  旁路单元: {voltage_result['bypass_units']}个")
                    if voltage_result['error_units'] > 0:
                        print(f"  解析错误单元: {voltage_result['error_units']}个")
                else:
                    print(f"  电压解析失败: {voltage_result.get('error')}")
                    continue

                print()

                # 生成最终的MQTT格式数据
                print("步骤3: 生成MQTT格式数据")
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                
                # 只调用一次parse_d0_data_to_json，同时完成显示和保存
                output_file = "data/d0_parsed_data.json"
                json_result = parser.parse_d0_data_to_json(
                    raw_hex,
                    timestamp=timestamp_str,
                    output_file=output_file
                )
                print("MQTT格式解析结果:")
                print(json_result)

                break  # 只处理第一个找到的D0数据

        if not d0_data_found:
            print("在realtime_data.json中未找到有效的D0命令数据")
            print("请确保文件中包含command='D0'且success=true的数据项")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()