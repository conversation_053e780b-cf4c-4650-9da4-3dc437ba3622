# 串口规则处理系统

## 项目概述

这是一个功能完整的串口通信和数据处理系统，用于处理串口设备的通信协议和数据解析。系统支持多种命令协议解析、MQTT数据推送、参数写入功能，并提供完整的服务管理和监控能力。

## 项目结构

```
SerialPortRule/
├── src/                        # 源代码目录
│   ├── core/                   # 核心服务模块
│   │   └── simple_main.py      # 主要的串口服务程序
│   ├── parsers/                # 数据解析器模块
│   │   ├── c0_data_parser.py   # C0命令数据解析器（IO状态）
│   │   ├── c5_data_parser.py   # C5命令数据解析器（版本信息）
│   │   ├── c59_data_parser.py  # C59命令数据解析器（调试参数）
│   │   ├── d6_data_parser.py   # D6命令数据解析器（A相单元版本）
│   │   ├── d7_data_parser.py   # D7命令数据解析器（B相单元版本）
│   │   └── d8_data_parser.py   # D8命令数据解析器（C相单元版本）
│   └── services/               # 服务模块
│       ├── data_parser_service.py # 数据解析器服务（独立运行）
│       ├── mqtt_publisher.py   # MQTT推送服务
│       └── config_windows.py   # Windows配置文件
├── tools/                      # 工具脚本目录
│   ├── manage_service.sh       # 服务管理脚本
│   └── start_data_parser_service.sh # 数据解析器服务启动脚本
├── tests/                      # 测试文件目录
│   └── test_data_parser_service.py # 数据解析器服务测试
├── config/                     # 配置文件目录
│   ├── service.conf            # Linux服务配置文件
│   ├── service_windows.conf    # Windows服务配置文件
│   ├── protocol.json           # 通信协议配置文件
│   └── mqtt_config.json        # MQTT服务器配置文件
├── data/                       # 数据目录
│   ├── realtime_data.json      # 实时数据文件
│   ├── 59_data.json            # C59命令专用数据文件
│   ├── write_cmd_data.json     # 写入命令数据文件
│   ├── startup_sequence.json   # 启动序列数据文件
│   ├── c0_parsed_data.json     # C0解析后的数据
│   ├── c5_parsed_data.json     # C5解析后的数据
│   ├── 59_parsed_data.json     # C59解析后的数据
│   ├── d6_parsed_data.json     # D6解析后的数据
│   ├── d7_parsed_data.json     # D7解析后的数据
│   ├── d8_parsed_data.json     # D8解析后的数据
│   ├── data_0.json             # 循环数据文件0
│   ├── data_1.json             # 循环数据文件1
│   └── data_2.json             # 循环数据文件2
├── logs/                       # 日志目录
├── docs/                       # 文档目录
├── scripts/                    # 脚本目录
│   ├── start_service.sh        # 主服务启动脚本
│   └── stop_service.sh         # 主服务停止脚本
├── deployment/                 # 部署相关文件
│   ├── data-parser.service     # 数据解析器服务配置文件
│   ├── serialport-service.service # 主服务配置文件
│   ├── serialport-logrotate.conf # 日志轮转配置
│   └── setup_logrotate.sh      # 日志轮转安装脚本
├── requirements.txt            # Python依赖包列表
└── README.md                   # 项目说明文档
```

## 功能特性

### 主要服务

1. **串口通信服务** (`simple_main.py`)
   - 处理串口设备通信
   - 执行启动序列和循环序列
   - 生成实时数据文件
   - 支持写入命令功能（A1-A6、AA-AB等控制命令）
   - 支持参数修改功能（57命令写入调试参数）
   - 数据轮转存储机制

2. **数据解析器服务** (`data_parser_service.py`)
   - 独立运行的数据处理服务
   - 监控数据文件变化
   - 自动触发相应的数据解析器
   - 支持C0、C5、C59、D6、D7、D8六种数据格式解析
   - 异步处理，防重复运行

3. **MQTT推送服务** (`mqtt_publisher.py`)
   - 多通道MQTT数据推送
   - 支持C0、C5、D6、D7、D8、59数据推送
   - 自动重连和错误处理
   - 支持远程参数写入功能

### 数据解析器

1. **C0数据解析器** (`c0_data_parser.py`)
   - 解析C0命令返回的IO状态数据
   - 前4字节输入IO状态，后4字节输出IO状态
   - 生成标准格式的JSON输出

2. **C5数据解析器** (`c5_data_parser.py`)
   - 解析C5命令返回的版本信息
   - 支持BCD码转换和模块版本解析
   - 包含硬件版本、软件版本、协议版本等信息

3. **C59数据解析器** (`c59_data_parser.py`)
   - 解析C59命令返回的调试参数数据
   - 支持Float_32bit和Uint_32bit数据类型转换
   - 处理259帧参数数据
   - 生成C0格式兼容的输出

4. **D6数据解析器** (`d6_data_parser.py`)
   - 解析D6命令返回的A相单元程序版本信息
   - 支持12个单元的版本信息解析
   - BCD码格式版本号转换

5. **D7数据解析器** (`d7_data_parser.py`)
   - 解析D7命令返回的B相单元程序版本信息
   - 支持12个单元的版本信息解析
   - BCD码格式版本号转换

6. **D8数据解析器** (`d8_data_parser.py`)
   - 解析D8命令返回的C相单元程序版本信息
   - 支持12个单元的版本信息解析
   - BCD码格式版本号转换

## 安装和使用

### 环境要求

- Python 3.7+
- Linux操作系统
- 串口设备访问权限

### 安装依赖

```bash
# 安装Python依赖包
pip3 install -r requirements.txt

# 主要依赖包包括：
# - pyserial>=3.5      # 串口通信
# - watchdog>=2.1.0    # 文件监控
# - aiofiles>=0.8.0    # 异步文件操作
# - paho-mqtt          # MQTT客户端（可选，用于MQTT功能）
```

### 运行服务

#### 1. 启动串口通信服务

```bash
# 直接运行
python3 src/core/simple_main.py

# 或使用系统服务（推荐）
sudo ./scripts/start_service.sh
```

#### 2. 启动数据解析器服务

使用启动脚本（推荐）：
```bash
# 使用bash运行（重要：不要使用sh）
bash tools/start_data_parser_service.sh
```

或直接运行：
```bash
python3 src/services/data_parser_service.py
```

或使用系统服务：
```bash
# 安装并启动服务
sudo ./tools/manage_service.sh install
sudo ./tools/manage_service.sh start
```

#### 3. 启动MQTT推送服务（可选）

```bash
# 启动C0数据推送
python3 src/services/mqtt_publisher.py

# 启动59参数数据推送
python3 src/services/mqtt_publisher.py --channel 59

# 启动多通道推送
python3 src/services/mqtt_publisher.py --multi-channel
```

### 服务特性

#### 数据解析器服务特性

- **文件监控**: 自动监控`data/`目录下的数据文件变化
- **智能触发**: 
  - 当`realtime_data.json`文件更新时，自动运行C0、C5、D6、D7、D8解析器
  - 当`59_data.json`文件更新时，自动运行C59解析器
- **防重复运行**: 避免同一解析器同时运行多个实例
- **初始解析**: 服务启动时自动执行一次完整解析
- **异步处理**: 使用异步编程提高处理效率

#### MQTT推送服务特性

- **多通道支持**: 支持C0、59、C5_D6_D7_D8三个独立通道
- **自动重连**: 网络断开时自动重连MQTT服务器
- **远程控制**: 支持通过MQTT接收远程参数写入指令
- **数据格式化**: 自动将解析数据转换为MQTT标准格式
- **定时推送**: 支持定时推送和事件触发推送

#### 写入命令功能

- **控制命令**: 支持A1-A6、AA-AB等设备控制命令
- **参数修改**: 支持57命令修改调试参数
- **命令队列**: 写入命令队列管理，避免冲突
- **确认机制**: 完整的命令发送-确认-验证流程

#### 数据流程

1. **数据采集**: `src/core/simple_main.py` 通过串口采集数据
   - 生成 `realtime_data.json`（C0、C1、C4、D0-D5数据）
   - 生成 `59_data.json`（C59调试参数数据）
   - 生成 `startup_sequence.json`（C5、D6、D7、D8启动序列数据）
   - 生成 `write_cmd_data.json`（写入命令数据）

2. **数据解析**: `src/services/data_parser_service.py` 监控文件变化并触发解析
   - `src/parsers/c0_data_parser.py` → `c0_parsed_data.json`
   - `src/parsers/c5_data_parser.py` → `c5_parsed_data.json`
   - `src/parsers/c59_data_parser.py` → `59_parsed_data.json`
   - `src/parsers/d6_data_parser.py` → `d6_parsed_data.json`
   - `src/parsers/d7_data_parser.py` → `d7_parsed_data.json`
   - `src/parsers/d8_data_parser.py` → `d8_parsed_data.json`

3. **数据推送**: `src/services/mqtt_publisher.py` 推送解析数据到MQTT服务器
   - C0通道：推送IO状态数据
   - 59通道：推送调试参数数据（支持远程写入）
   - C5_D6_D7_D8通道：推送版本信息数据

## 配置说明

### 数据目录

默认数据目录为`data/`，包含以下文件：

**原始数据文件**：
- `realtime_data.json`: 串口服务生成的实时数据（C0、C1、C4、D0-D5）
- `59_data.json`: C59命令的专用数据文件
- `startup_sequence.json`: 启动序列数据文件（C5、D6、D7、D8）
- `write_cmd_data.json`: 写入命令数据文件
- `data_0.json`, `data_1.json`, `data_2.json`: 循环数据文件

**解析结果文件**：
- `c0_parsed_data.json`: C0解析结果（IO状态）
- `c5_parsed_data.json`: C5解析结果（版本信息）
- `59_parsed_data.json`: C59解析结果（调试参数）
- `d6_parsed_data.json`: D6解析结果（A相单元版本）
- `d7_parsed_data.json`: D7解析结果（B相单元版本）
- `d8_parsed_data.json`: D8解析结果（C相单元版本）

### 配置文件

#### 1. 串口通信配置 (`config/service.conf`)
- 串口参数配置
- 命令序列配置
- 数据存储配置
- 日志配置

#### 2. 通信协议配置 (`config/protocol.json`)
- 各命令的帧格式定义
- 数据长度和类型配置
- 校验规则配置

#### 3. MQTT服务配置 (`config/mqtt_config.json`)
- MQTT服务器连接信息
- 多通道配置（C0、59、C5_D6_D7_D8）
- 推送间隔和主题配置

### 服务配置

#### 数据解析器服务配置
- 数据目录路径（默认：`data`）
- 文件监控间隔
- 解析器超时设置
- 异步处理配置

#### MQTT推送服务配置
- 连接超时和重试配置
- 推送频率控制
- 数据格式化选项
- 远程控制功能开关

## 开发说明

### 代码规范

- 使用相对路径引用数据文件
- 遵循PEP 8编码规范
- 添加适当的错误处理和日志输出
- 使用类型注解提高代码可读性
- 添加函数级注释说明功能

### 扩展新的解析器

1. 创建新的解析器文件（如`cx_data_parser.py`）
2. 在`data_parser_service.py`中添加相应的监控逻辑
3. 在`mqtt_publisher.py`中添加MQTT推送支持
4. 更新`protocol.json`配置文件
5. 更新启动脚本和文档

### 添加新的MQTT通道

1. 在`mqtt_config.json`中添加新通道配置
2. 在`mqtt_publisher.py`中实现对应的推送逻辑
3. 添加数据格式转换函数
4. 测试连接和数据推送功能

### 添加新的写入命令

1. 在`simple_main.py`中的`write_commands_map`添加命令定义
2. 实现命令帧构造逻辑
3. 添加命令执行和确认流程
4. 更新文档说明

## 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   pip3 install --upgrade pip
   pip3 install -r requirements.txt
   
   # 如果安装paho-mqtt失败
   pip3 install paho-mqtt
   ```

2. **权限问题**
   ```bash
   # 给脚本执行权限
   chmod +x tools/start_data_parser_service.sh
   chmod +x scripts/start_service.sh
   
   # 串口设备权限
   sudo usermod -a -G dialout $USER
   # 需要重新登录生效
   ```

3. **串口连接问题**
   - 检查串口设备是否存在：`ls /dev/ttyUSB*` 或 `ls /dev/ttyACM*`
   - 检查串口权限：`ls -l /dev/ttyUSB0`
   - 确认串口参数配置正确

4. **数据文件不存在**
   - 确保`data/`目录存在
   - 检查串口服务是否正常运行
   - 查看串口通信日志

5. **解析器无响应**
   - 检查文件监控是否正常
   - 查看服务日志输出
   - 确认数据文件格式正确
   - 检查Python路径配置

6. **MQTT连接失败**
   - 检查网络连接
   - 验证MQTT服务器地址和端口
   - 确认用户名和密码正确
   - 查看MQTT服务日志

7. **脚本运行错误**
   ```bash
   # 错误：使用sh运行脚本
   sh tools/start_data_parser_service.sh  # 会出现"Bad substitution"错误
   
   # 正确：使用bash运行脚本
   bash tools/start_data_parser_service.sh
   ```

### 日志和调试

服务运行时会输出详细的日志信息，包括：
- 串口连接状态
- 命令发送和接收
- 文件变化检测
- 解析器启动和完成状态
- MQTT连接和推送状态
- 错误信息和异常处理

### 系统服务管理

```bash
# 查看服务状态
sudo systemctl status serialport-service
sudo systemctl status data-parser

# 查看服务日志
sudo journalctl -u serialport-service -f
sudo journalctl -u data-parser -f

# 重启服务
sudo systemctl restart serialport-service
sudo systemctl restart data-parser
```

## 更新日志

### v2.0.0 (最新版本)
- **新增功能**：
  - 添加D6、D7、D8数据解析器（三相单元版本信息）
  - 实现MQTT多通道数据推送功能
  - 支持写入命令功能（A1-A6、AA-AB控制命令）
  - 支持参数修改功能（57命令写入调试参数）
  - 添加远程MQTT参数写入功能
  - 实现数据轮转存储机制
- **系统改进**：
  - 完善系统服务管理（systemd服务）
  - 优化异步处理性能
  - 增强错误处理和日志记录
  - 添加服务监控和健康检查
- **配置增强**：
  - 新增MQTT配置文件
  - 扩展协议配置支持
  - 完善服务配置选项

### v1.0.0
- 实现基础串口通信功能
- 添加C0、C5、C59数据解析器
- 创建独立的数据解析器服务
- 支持文件监控和自动触发
- 统一文件命名规范
- 使用相对路径提高可移植性

## 许可证

本项目采用MIT许可证。