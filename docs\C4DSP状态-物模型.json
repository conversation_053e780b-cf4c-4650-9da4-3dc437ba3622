{"properties": [{"id": "HMI_30113", "name": "远程SVG输出无功", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30121", "name": "负序电流d轴分量", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30119", "name": "负序电压q轴分量", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30117", "name": "负序电压d轴分量", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30115", "name": "电压无功模式电压调节器输出", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30109", "name": "主控CPU时间利用率", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30105", "name": "Id电流环PI调节器积分输出", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30107", "name": "Iq电流环PI调节器积分输出", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30123", "name": "负序电流q轴分量", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}, {"id": "HMI_30111", "name": "故障自动启动次数", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": "100", "min": "0", "step": 1, "type": "decimal", "unit": ""}}], "functions": [], "events": []}