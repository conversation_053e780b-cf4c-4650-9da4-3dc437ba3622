#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C0命令数据解析器
根据C0数据对应规则将十六进制数据转换为IO点位状态JSON格式
"""

import json
from datetime import datetime
from typing import List, Dict, Any


class C0DataParser:
    """
    C0命令数据解析器类
    用于解析C0命令返回的IO输入输出状态数据
    """

    def __init__(self):
        """
        初始化解析器，定义IO点位映射关系
        """
        # 32位IO输入点位映射 (0x00000000对应的位)
        self.input_mapping = {
            0: {"id": "HMI_30018_0", "name": "启动按钮输入"},
            1: {"id": "HMI_30018_1", "name": "停止按钮输入"},
            2: {"id": "HMI_30018_2", "name": "复位按钮输入"},
            3: {"id": "HMI_30018_3", "name": "备用按钮输入"},
            4: {"id": "HMI_30018_4", "name": "急停按钮输入"},
            5: {"id": "HMI_30018_5", "name": "SVG断路器输入"},
            6: {"id": "HMI_30018_6", "name": "启动柜接触器输入"},
            7: {"id": "HMI_30018_7", "name": "启动柜隔离刀闸输入"},
            8: {"id": "HMI_30018_8", "name": "启动柜接地刀闸输入"},
            9: {"id": "HMI_30018_9", "name": "远方旋钮输入"},
            10: {"id": "HMI_30018_10", "name": "就地旋钮输入"},
            11: {"id": "HMI_30018_11", "name": "水冷系统电源故障输入"},
            12: {"id": "HMI_30018_12", "name": "水冷系统预警输入"},
            13: {"id": "HMI_30018_13", "name": "水冷系统请求跳闸输入"},
            14: {"id": "HMI_30018_14", "name": "水冷系统请求停止输入"},
            15: {"id": "HMI_30018_15", "name": "水冷系统运行/停止输入"},
            16: {"id": "HMI_30019_0", "name": "母联1状态输入"},
            17: {"id": "HMI_30019_1", "name": "母联2状态输入"},
            18: {"id": "HMI_30019_2", "name": "风机运行状态输入"},
            19: {"id": "HMI_30019_3", "name": "备用"},
            20: {"id": "HMI_30019_4", "name": "输入21"},
            21: {"id": "HMI_30019_5", "name": "输入22"},
            22: {"id": "HMI_30019_6", "name": "输入23"},
            23: {"id": "HMI_30019_7", "name": "输入24"},
            24: {"id": "HMI_30019_8", "name": "输入25"},
            25: {"id": "HMI_30019_9", "name": "输入26"},
            26: {"id": "HMI_30019_10", "name": "输入27"},
            27: {"id": "HMI_30019_11", "name": "输入28"},
            28: {"id": "HMI_30019_12", "name": "输入29"},
            29: {"id": "HMI_30019_13", "name": "输入30"},
            30: {"id": "HMI_30019_14", "name": "输入31"},
            31: {"id": "HMI_30019_15", "name": "输入32"}
        }

        # 32位IO输出点位映射 (0x00001016对应的位)
        self.output_mapping = {
            0: {"id": "HMI_30020_0", "name": "SVG断路器允许合闸输出"},
            1: {"id": "HMI_30020_1", "name": "SVG断路器合闸命令输出"},
            2: {"id": "HMI_30020_2", "name": "SVG断路器分闸命令输出"},
            3: {"id": "HMI_30020_3", "name": "启动柜接触器合闸命令输出"},
            4: {"id": "HMI_30020_4", "name": "启动柜接触器分闸命令输出"},
            5: {"id": "HMI_30020_5", "name": "运行指示灯输出"},
            6: {"id": "HMI_30020_6", "name": "故障指示灯输出"},
            7: {"id": "HMI_30020_7", "name": "就绪指示灯输出"},
            8: {"id": "HMI_30020_8", "name": "报警指示灯输出"},
            9: {"id": "HMI_30020_9", "name": "保留指示灯输出"},
            10: {"id": "HMI_30020_10", "name": "SVG运行去水冷输出"},
            11: {"id": "HMI_30020_11", "name": "水冷启动输出"},
            12: {"id": "HMI_30020_12", "name": "水冷停止输出"},
            13: {"id": "HMI_30020_13", "name": "远程故障输出"},
            14: {"id": "HMI_30020_14", "name": "风机合闸输出"},
            15: {"id": "HMI_30020_15", "name": "故障报警脉冲输出"},
            16: {"id": "HMI_30021_0", "name": "输出17"},
            17: {"id": "HMI_30021_1", "name": "输出18"},
            18: {"id": "HMI_30021_2", "name": "输出19"},
            19: {"id": "HMI_30021_3", "name": "输出20"},
            20: {"id": "HMI_30021_4", "name": "输出21"},
            21: {"id": "HMI_30021_5", "name": "输出22"},
            22: {"id": "HMI_30021_6", "name": "输出23"},
            23: {"id": "HMI_30021_7", "name": "输出24"},
            24: {"id": "HMI_30021_8", "name": "输出25"},
            25: {"id": "HMI_30021_9", "name": "输出26"},
            26: {"id": "HMI_30021_10", "name": "输出27"},
            27: {"id": "HMI_30021_11", "name": "输出28"},
            28: {"id": "HMI_30021_12", "name": "输出29"},
            29: {"id": "HMI_30021_13", "name": "输出30"},
            30: {"id": "HMI_30021_14", "name": "输出31"},
            31: {"id": "HMI_30021_15", "name": "输出32"}
        }

    def parse_hex_to_bits(self, hex_value: str) -> List[int]:
        """
        将十六进制字符串转换为32位二进制数组

        Args:
            hex_value: 十六进制字符串，如 "0x00001016"

        Returns:
            32位二进制数组，索引0对应最低位
        """
        # 移除0x前缀并转换为整数
        if hex_value.startswith('0x'):
            hex_value = hex_value[2:]

        int_value = int(hex_value, 16)

        # 转换为32位二进制数组
        bits = []
        for i in range(32):
            bits.append((int_value >> i) & 1)

        return bits

    def parse_c0_data(self, data_hex_formatted: List[str], timestamp: str = None) -> List[Dict[str, Any]]:
        """
        解析C0命令的data_hex_formatted数据

        Args:
            data_hex_formatted: C0命令返回的格式化十六进制数据，如 ["0x00000000", "0x00001016"]
            timestamp: 时间戳，如果不提供则使用当前时间

        Returns:
            包含所有IO点位状态的JSON格式列表
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        result = []

        if len(data_hex_formatted) != 2:
            raise ValueError("C0命令数据格式错误，应包含2个十六进制值")

        input_hex = data_hex_formatted[0]  # 输入IO状态
        output_hex = data_hex_formatted[1]  # 输出IO状态

        # 解析输入IO状态
        input_bits = self.parse_hex_to_bits(input_hex)
        for bit_index, bit_value in enumerate(input_bits):
            if bit_index in self.input_mapping:
                point_info = self.input_mapping[bit_index]
                result.append({
                    "id": point_info["id"],
                    "name": point_info["name"],
                    "ts": timestamp,
                    "value": str(bit_value)
                })

        # 解析输出IO状态
        output_bits = self.parse_hex_to_bits(output_hex)
        for bit_index, bit_value in enumerate(output_bits):
            if bit_index in self.output_mapping:
                point_info = self.output_mapping[bit_index]
                result.append({
                    "id": point_info["id"],
                    "name": point_info["name"],
                    "ts": timestamp,
                    "value": str(bit_value)
                })

        return result

    def parse_c0_data_to_json(self, data_hex_formatted: List[str], timestamp: str = None,
                             output_file: str = None, pretty_print: bool = True) -> str:
        """
        解析C0命令数据并输出为JSON字符串或保存到文件

        Args:
            data_hex_formatted: C0命令返回的格式化十六进制数据
            timestamp: 时间戳
            output_file: 输出文件路径，如果不提供则返回JSON字符串
            pretty_print: 是否格式化输出JSON

        Returns:
            JSON字符串
        """
        parsed_data = self.parse_c0_data(data_hex_formatted, timestamp)

        if pretty_print:
            json_str = json.dumps(parsed_data, ensure_ascii=False, indent=4)
        else:
            json_str = json.dumps(parsed_data, ensure_ascii=False)

        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"解析结果已保存到: {output_file}")

        return json_str


def main():
    """
    主函数，从realtime_data.json文件中读取C0命令数据并解析
    """
    parser = C0DataParser()

    # 读取realtime_data.json文件
    realtime_data_file = "data/realtime_data.json"

    try:
        with open(realtime_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("C0命令数据解析器 - 从realtime_data.json读取数据")
        print("=" * 60)

        # 查找C0命令数据
        c0_data_found = False
        for item in data:
            if item.get('command') == 'C0' and item.get('success') and item.get('data_hex_formatted'):
                c0_data_found = True

                print(f"找到C0命令数据:")
                print(f"时间戳: {item.get('timestamp')}")
                print(f"原始数据: {item.get('raw_hex')}")
                print(f"格式化数据: {item.get('data_hex_formatted')}")
                print(f"输入IO状态: {item['data_hex_formatted'][0]} (32位)")
                print(f"输出IO状态: {item['data_hex_formatted'][1]} (32位)")
                print()

                # 解析数据
                timestamp_str = datetime.fromtimestamp(item['timestamp']).strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                json_result = parser.parse_c0_data_to_json(
                    item['data_hex_formatted'],
                    timestamp=timestamp_str
                )
                print("解析结果:")
                print(json_result)

                # 保存到文件
                output_file = "data/c0_parsed_data.json"
                parser.parse_c0_data_to_json(
                    item['data_hex_formatted'],
                    timestamp=timestamp_str,
                    output_file=output_file
                )

                break  # 只处理第一个找到的C0数据

        if not c0_data_found:
            print("在realtime_data.json中未找到有效的C0命令数据")
            print("请确保文件中包含command='C0'且success=true的数据项")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {realtime_data_file}")
        print("请确保realtime_data.json文件存在")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
    except Exception as e:
        print(f"处理错误: {e}")


if __name__ == "__main__":
    main()