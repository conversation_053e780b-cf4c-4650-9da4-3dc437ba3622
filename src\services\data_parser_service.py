#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析器服务
持续运行c0、c5、c59数据解析器，监控数据文件变化并自动处理
"""

import asyncio
import json
import os
import subprocess
import time
import threading
from pathlib import Path
from typing import Dict, Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class DataFileHandler(FileSystemEventHandler):
    """数据文件变化监控处理器"""

    def __init__(self, service):
        self.service = service
        self.last_modified = {}

    def on_modified(self, event):
        if event.is_directory:
            return

        file_path = event.src_path
        file_name = os.path.basename(file_path)

        # 防止重复触发
        current_time = time.time()
        if file_path in self.last_modified:
            if current_time - self.last_modified[file_path] < 0.5:  # 500毫秒内的重复事件忽略
                return
        self.last_modified[file_path] = current_time

        # 检查是否是我们关心的文件
        if file_name == 'realtime_data.json':
            print(f"文件变化: {file_name}")
            self.service.schedule_parsers(['c0', 'c4', 'c5', 'c59', 'd0', 'd1', 'd2', 'd3', 'd4', 'd5', 'd6', 'd7', 'd8'])


class DataParserService:
    """数据解析器服务"""

    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.running = False
        self.observer = None
        self.handler = None
        self.loop = None

        # 解析器脚本路径
        self.base_dir = Path(__file__).parent.parent.parent  # 回到项目根目录
        self.c0_parser = self.base_dir / "src" / "parsers" / "c0_data_parser.py"
        self.c4_parser = self.base_dir / "src" / "parsers" / "c4_data_parser.py"
        self.c5_parser = self.base_dir / "src" / "parsers" / "c5_data_parser.py"
        self.c59_parser = self.base_dir / "src" / "parsers" / "c59_data_parser.py"
        self.d0_parser = self.base_dir / "src" / "parsers" / "d0_data_parser.py"
        self.d1_parser = self.base_dir / "src" / "parsers" / "d1_data_parser.py"
        self.d2_parser = self.base_dir / "src" / "parsers" / "d2_data_parser.py"
        self.d3_parser = self.base_dir / "src" / "parsers" / "d3_data_parser.py"
        self.d4_parser = self.base_dir / "src" / "parsers" / "d4_data_parser.py"
        self.d5_parser = self.base_dir / "src" / "parsers" / "d5_data_parser.py"
        self.d6_parser = self.base_dir / "src" / "parsers" / "d6_data_parser.py"
        self.d7_parser = self.base_dir / "src" / "parsers" / "d7_data_parser.py"
        self.d8_parser = self.base_dir / "src" / "parsers" / "d8_data_parser.py"

        # 运行状态跟踪
        self.parser_running = {
            'c0': False,
            'c4': False,
            'c5': False,
            'c59': False,
            'd0': False,
            'd1': False,
            'd2': False,
            'd3': False,
            'd4': False,
            'd5': False,
            'd6': False,
            'd7': False,
            'd8': False
        }

    def schedule_parsers(self, parser_types):
        """线程安全地调度解析器运行"""
        if self.loop and not self.loop.is_closed():
            for parser_type in parser_types:
                if parser_type == 'c0':
                    asyncio.run_coroutine_threadsafe(self.run_c0_parser(), self.loop)
                elif parser_type == 'c4':
                    asyncio.run_coroutine_threadsafe(self.run_c4_parser(), self.loop)
                elif parser_type == 'c5':
                    asyncio.run_coroutine_threadsafe(self.run_c5_parser(), self.loop)
                elif parser_type == 'c59':
                    asyncio.run_coroutine_threadsafe(self.run_c59_parser(), self.loop)
                elif parser_type == 'd0':
                    asyncio.run_coroutine_threadsafe(self.run_d0_parser(), self.loop)
                elif parser_type == 'd1':
                    asyncio.run_coroutine_threadsafe(self.run_d1_parser(), self.loop)
                elif parser_type == 'd2':
                    asyncio.run_coroutine_threadsafe(self.run_d2_parser(), self.loop)
                elif parser_type == 'd3':
                    asyncio.run_coroutine_threadsafe(self.run_d3_parser(), self.loop)
                elif parser_type == 'd4':
                    asyncio.run_coroutine_threadsafe(self.run_d4_parser(), self.loop)
                elif parser_type == 'd5':
                    asyncio.run_coroutine_threadsafe(self.run_d5_parser(), self.loop)
                elif parser_type == 'd6':
                    asyncio.run_coroutine_threadsafe(self.run_d6_parser(), self.loop)
                elif parser_type == 'd7':
                    asyncio.run_coroutine_threadsafe(self.run_d7_parser(), self.loop)
                elif parser_type == 'd8':
                    asyncio.run_coroutine_threadsafe(self.run_d8_parser(), self.loop)

    async def run_c0_parser(self):
        """运行c0数据解析器"""
        if self.parser_running['c0']:
            return

        self.parser_running['c0'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.c0_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("C0解析完成")
            else:
                print(f"C0解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"C0解析错误: {e}")
        finally:
            self.parser_running['c0'] = False

    async def run_c4_parser(self):
        """运行c4数据解析器"""
        if self.parser_running['c4']:
            return

        self.parser_running['c4'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.c4_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("C4解析完成")
            else:
                print(f"C4解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"C4解析错误: {e}")
        finally:
            self.parser_running['c4'] = False

    async def run_c5_parser(self):
        """运行c5数据解析器"""
        if self.parser_running['c5']:
            return

        self.parser_running['c5'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.c5_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("C5解析完成")
            else:
                print(f"C5解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"C5解析错误: {e}")
        finally:
            self.parser_running['c5'] = False

    async def run_c59_parser(self):
        """运行c59数据解析器"""
        if self.parser_running['c59']:
            return

        self.parser_running['c59'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.c59_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("C59解析完成")
            else:
                print(f"C59解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"C59解析错误: {e}")
        finally:
            self.parser_running['c59'] = False

    async def run_d0_parser(self):
        """运行d0数据解析器"""
        if self.parser_running['d0']:
            return

        self.parser_running['d0'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d0_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D0解析完成")
            else:
                print(f"D0解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D0解析错误: {e}")
        finally:
            self.parser_running['d0'] = False

    async def run_d1_parser(self):
        """运行d1数据解析器"""
        if self.parser_running['d1']:
            return

        self.parser_running['d1'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d1_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D1解析完成")
            else:
                print(f"D1解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D1解析错误: {e}")
        finally:
            self.parser_running['d1'] = False

    async def run_d2_parser(self):
        """运行d2数据解析器"""
        if self.parser_running['d2']:
            return

        self.parser_running['d2'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d2_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D2解析完成")
            else:
                print(f"D2解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D2解析错误: {e}")
        finally:
            self.parser_running['d2'] = False

    async def run_d3_parser(self):
        """运行d3数据解析器"""
        if self.parser_running['d3']:
            return

        self.parser_running['d3'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d3_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D3解析完成")
            else:
                print(f"D3解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D3解析错误: {e}")
        finally:
            self.parser_running['d3'] = False

    async def run_d4_parser(self):
        """运行d4数据解析器"""
        if self.parser_running['d4']:
            return

        self.parser_running['d4'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d4_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D4解析完成")
            else:
                print(f"D4解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D4解析错误: {e}")
        finally:
            self.parser_running['d4'] = False

    async def run_d5_parser(self):
        """运行d5数据解析器"""
        if self.parser_running['d5']:
            return

        self.parser_running['d5'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d5_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D5解析完成")
            else:
                print(f"D5解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D5解析错误: {e}")
        finally:
            self.parser_running['d5'] = False

    async def run_d6_parser(self):
        """运行d6数据解析器"""
        if self.parser_running['d6']:
            return

        self.parser_running['d6'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d6_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D6解析完成")
            else:
                print(f"D6解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D6解析错误: {e}")
        finally:
            self.parser_running['d6'] = False

    async def run_d7_parser(self):
        """运行d7数据解析器"""
        if self.parser_running['d7']:
            return

        self.parser_running['d7'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d7_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D7解析完成")
            else:
                print(f"D7解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D7解析错误: {e}")
        finally:
            self.parser_running['d7'] = False

    async def run_d8_parser(self):
        """运行d8数据解析器"""
        if self.parser_running['d8']:
            return

        self.parser_running['d8'] = True
        try:
            result = await asyncio.create_subprocess_exec(
                'python3', str(self.d8_parser),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await result.communicate()

            if result.returncode == 0:
                print("D8解析完成")
            else:
                print(f"D8解析失败: {stderr.decode().strip()}")

        except Exception as e:
            print(f"D8解析错误: {e}")
        finally:
            self.parser_running['d8'] = False

    async def initial_parse(self):
        """初始解析 - 启动时运行一次所有解析器"""
        print("执行初始解析...")

        # 检查数据文件是否存在
        realtime_data_file = self.data_dir / "realtime_data.json"
        data_59_file = self.data_dir / "59_data.json"

        tasks = []

        if realtime_data_file.exists():
            tasks.append(self.run_c0_parser())
            tasks.append(self.run_c4_parser())
            tasks.append(self.run_c5_parser())
            tasks.append(self.run_d0_parser())
            tasks.append(self.run_d1_parser())
            tasks.append(self.run_d2_parser())
            tasks.append(self.run_d3_parser())
            tasks.append(self.run_d4_parser())
            tasks.append(self.run_d5_parser())
            tasks.append(self.run_d6_parser())
            tasks.append(self.run_d7_parser())
            tasks.append(self.run_d8_parser())

        if data_59_file.exists():
            tasks.append(self.run_c59_parser())

        if tasks:
            await asyncio.gather(*tasks)
        else:
            print("等待数据文件...")

    def start_file_monitoring(self):
        """启动文件监控"""
        if not self.data_dir.exists():
            self.data_dir.mkdir(parents=True, exist_ok=True)

        self.handler = DataFileHandler(self)
        self.observer = Observer()
        self.observer.schedule(self.handler, str(self.data_dir), recursive=False)
        self.observer.start()
        print(f"开始监控数据目录: {self.data_dir}")

    def stop_file_monitoring(self):
        """停止文件监控"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            print("文件监控已停止")

    async def run(self):
        """运行服务"""
        print("数据解析器服务启动...")
        self.running = True
        self.loop = asyncio.get_running_loop()  # 获取当前事件循环

        try:
            # 启动文件监控
            self.start_file_monitoring()

            # 执行初始解析
            await self.initial_parse()

            print("服务运行中，监控文件变化...")

            # 保持服务运行
            while self.running:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            print("\n收到中断信号，正在停止服务...")
        except Exception as e:
            print(f"服务运行出错: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """停止服务"""
        print("正在停止数据解析器服务...")
        self.running = False

        # 停止文件监控
        self.stop_file_monitoring()

        # 等待所有解析器完成
        while any(self.parser_running.values()):
            print("等待解析器完成...")
            await asyncio.sleep(0.5)

        print("数据解析器服务已停止")


async def main():
    """主函数"""
    service = DataParserService()
    await service.run()


if __name__ == "__main__":
    asyncio.run(main())