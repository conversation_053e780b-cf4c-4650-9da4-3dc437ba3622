#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C1故障报警协议解析器映射验证脚本
验证故障点映射表与协议解析.md文件的一致性
"""

from c1_data_parser import C1DataParser


def load_reference_mapping():
    """
    从协议解析.md文件加载参考映射表
    """
    reference_map = {}
    
    try:
        with open('data/协议解析.md', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 解析第10-537行的故障点定义
        for line_num in range(9, 537):  # 第10行到第537行（0-based索引）
            if line_num < len(lines):
                line = lines[line_num].strip()
                if '\t' in line:
                    parts = line.split('\t', 1)
                    if len(parts) == 2:
                        fault_id = parts[0].strip()
                        fault_desc = parts[1].strip()
                        reference_map[fault_id] = fault_desc
        
        return reference_map
        
    except FileNotFoundError:
        print("错误: 找不到协议解析.md文件")
        return {}
    except Exception as e:
        print(f"读取协议解析.md文件时出错: {e}")
        return {}


def verify_mapping():
    """
    验证C1解析器的故障点映射表
    """
    print("C1故障报警协议解析器映射验证")
    print("=" * 50)
    
    # 加载参考映射表
    reference_map = load_reference_mapping()
    if not reference_map:
        print("无法加载参考映射表，验证终止")
        return
    
    print(f"从协议解析.md加载了 {len(reference_map)} 个参考故障点定义")
    
    # 创建解析器并获取映射表
    parser = C1DataParser()
    parser_map = parser.fault_mapping
    
    print(f"C1解析器包含 {len(parser_map)} 个故障点定义")
    print()
    
    # 验证一致性
    print("验证结果:")
    print("-" * 30)
    
    correct_count = 0
    missing_count = 0
    mismatch_count = 0
    
    for fault_id, reference_desc in reference_map.items():
        if fault_id in parser_map:
            parser_desc = parser_map[fault_id]
            if parser_desc == reference_desc:
                correct_count += 1
            else:
                mismatch_count += 1
                print(f"❌ {fault_id}: 不匹配")
                print(f"   参考: {reference_desc}")
                print(f"   解析器: {parser_desc}")
                print()
        else:
            missing_count += 1
            print(f"❌ {fault_id}: 解析器中缺失")
            print(f"   参考: {reference_desc}")
            print()
    
    # 检查解析器中多余的定义
    extra_count = 0
    for fault_id in parser_map:
        if fault_id not in reference_map:
            extra_count += 1
    
    # 输出统计结果
    print("验证统计:")
    print(f"✅ 正确匹配: {correct_count}")
    print(f"❌ 描述不匹配: {mismatch_count}")
    print(f"❌ 解析器中缺失: {missing_count}")
    print(f"ℹ️  解析器中额外: {extra_count}")
    print()
    
    total_reference = len(reference_map)
    accuracy = (correct_count / total_reference * 100) if total_reference > 0 else 0
    print(f"准确率: {accuracy:.1f}% ({correct_count}/{total_reference})")
    
    if mismatch_count == 0 and missing_count == 0:
        print("🎉 验证通过！所有故障点定义都与参考文档一致。")
    else:
        print("⚠️  发现不一致，需要修正解析器映射表。")
    
    return correct_count, mismatch_count, missing_count, extra_count


def show_sample_mappings():
    """
    显示一些示例映射
    """
    print("\n示例故障点映射:")
    print("-" * 30)
    
    parser = C1DataParser()
    
    # 显示各组的示例
    sample_groups = [24, 25, 28, 39, 40, 46, 54]
    
    for group in sample_groups:
        print(f"\n{group}组示例:")
        for bit in range(min(4, 16)):  # 只显示前4个
            fault_id = f"{group}_{bit}"
            if fault_id in parser.fault_mapping:
                print(f"  {fault_id}: {parser.fault_mapping[fault_id]}")


if __name__ == "__main__":
    verify_mapping()
    show_sample_mappings()
