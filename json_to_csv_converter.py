#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JSON到CSV转换器

此脚本用于将c1_parsed_data.json文件转换为CSV格式
"""

import json
import csv
import os


def convert_json_to_csv(json_file_path, csv_file_path):
    """
    将JSON文件转换为CSV文件
    
    Args:
        json_file_path: JSON文件路径
        csv_file_path: 输出CSV文件路径
    """
    # 读取JSON文件
    with open(json_file_path, 'r', encoding='utf-8') as json_file:
        data = json.load(json_file)
    
    # 如果数据为空，则返回
    if not data:
        print(f"警告: {json_file_path} 中没有数据")
        return
    
    # 获取字段名（假设所有记录具有相同的结构）
    fieldnames = data[0].keys()
    
    # 写入CSV文件
    with open(csv_file_path, 'w', encoding='utf-8', newline='') as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"转换完成! CSV文件已保存到: {csv_file_path}")


def main():
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_file_path = os.path.join(current_dir, 'data', 'c1_parsed_data.json')
    csv_file_path = os.path.join(current_dir, 'data', 'c1_parsed_data.csv')
    
    # 检查JSON文件是否存在
    if not os.path.exists(json_file_path):
        print(f"错误: 找不到文件 {json_file_path}")
        return
    
    # 执行转换
    convert_json_to_csv(json_file_path, csv_file_path)


if __name__ == "__main__":
    main()