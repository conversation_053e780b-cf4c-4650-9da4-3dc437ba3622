# 串口通信服务日志轮转配置
# 应用程序数据文件轮转
/home/<USER>/SerialPortRule/data/*.json {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    copytruncate
    maxsize 100M
    postrotate
        # 可选：发送信号给应用程序重新打开日志文件
        # /bin/kill -HUP `cat /var/run/serialport-service.pid 2> /dev/null` 2> /dev/null || true
    endscript
}

# 应用程序日志文件轮转
/home/<USER>/SerialPortRule/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    copytruncate
    maxsize 50M
    postrotate
        # 重新加载服务以重新打开日志文件
        /bin/systemctl reload-or-restart serialport-service.service 2> /dev/null || true
    endscript
}

# 启动序列数据文件特殊处理
/home/<USER>/SerialPortRule/data/startup_sequence.json {
    weekly
    rotate 12
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    copytruncate
    maxsize 200M
    # 启动序列文件不需要频繁轮转，保留更长时间
}