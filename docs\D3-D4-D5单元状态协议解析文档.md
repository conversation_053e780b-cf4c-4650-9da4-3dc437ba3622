# D3-D4-D5单元状态协议解析文档

## 1. 协议概述

本文档详细描述了D3、D4、D5三个协议的数据格式和解析方法，用于查询A、B、C三相单元的状态数据。这三个协议分别对应三相单元的状态查询，协议结构相似但命令字节不同。

## 2. 协议基本格式

### 2.1 协议结构

所有协议遵循统一的数据帧格式：

| 字段 | 长度 | 说明 |
|------|------|------|
| 帧头 | 2字节 | 固定为 `EB 90` |
| 地址 | 1字节 | 固定为 `01` |
| 功能码 | 1字节 | 固定为 `01` |
| 命令码 | 1字节 | D3/D4/D5 |
| 数据长度 | 1字节 | 发送时为`00`，接收时为`24` |
| 数据区 | 8字节(发送)/36字节(接收) | 发送时固定为 `00 00 00 00 00 00 00 00`，接收时为单元状态数据 |
| 校验码 | 1字节 | 根据命令计算 |
| 帧尾 | 2字节 | 固定为 `AA AB` |

## 3. 协议详细说明

### 3.1 D3协议 - A相单元状态查询

#### 3.1.1 发送命令
```
EB 90 01 01 D3 00 00 00 00 00 00 00 00 50 AA AB
```

#### 3.1.2 接收响应格式
```
EB 90 01 01 D3 24 00 00 00 [单元状态数据] [校验位] AA AB
```

#### 3.1.3 响应示例
```
EB 90 01 01 D3 24 00 00 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 A4 AA AB
```

### 3.2 D4协议 - B相单元状态查询

#### 3.2.1 发送命令
```
EB 90 01 01 D4 00 00 00 00 00 00 00 00 51 AA AB
```

#### 3.2.2 接收响应格式
```
EB 90 01 01 D4 24 00 00 00 [单元状态数据] [校验位] AA AB
```

#### 3.2.3 响应示例
```
EB 90 01 01 D4 24 00 00 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 A5 AA AB
```

### 3.3 D5协议 - C相单元状态查询

#### 3.3.1 发送命令
```
EB 90 01 01 D5 00 00 00 00 00 00 00 00 52 AA AB
```

#### 3.3.2 接收响应格式
```
EB 90 01 01 D5 24 00 00 00 [单元状态数据] [校验位] AA AB
```

#### 3.3.3 响应示例
```
EB 90 01 01 D5 24 00 00 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 01 00 01 00 A0 AA AB
```

## 4. 数据解析方法

### 4.1 单元状态数据格式

响应数据中的单元状态数据区域为36字节，每2字节表示一个单元的状态值，共18个单元位置（实际有效为12个单元）。

#### 4.1.1 数据排列
| 字节位置 | 对应单元 | 数据格式 |
|----------|----------|----------|
| 0-1 | 单元1 | 小端序16位无符号整数 |
| 2-3 | 单元2 | 小端序16位无符号整数 |
| 4-5 | 单元3 | 小端序16位无符号整数 |
| ... | ... | ... |
| 18-19 | 单元10 | 小端序16位无符号整数 |
| 20-23 | 单元11-12 | 通常为旁路状态 |
| 24-35 | 单元13-18 | 保留区域（通常为0） |

#### 4.1.2 状态值解析

每个单元的状态用2字节表示，采用小端序格式（低字节在前，高字节在后）。例如：`04 00` 表示停止状态。

### 4.2 状态码含义

单元状态码是一个16位的值，其中不同的位表示不同的状态，一个单元可能同时具有多个状态（位运算的结果）。

| 状态码 | 十六进制 | 含义 |
|--------|----------|------|
| 0000 0000 0000 0001 | 0x0001 | 运行 |
| 0000 0000 0000 0010 | 0x0002 | 故障 |
| 0000 0000 0000 0100 | 0x0004 | 停止 |
| 0000 0000 0000 1000 | 0x0008 | 电源故障 |
| 0000 0000 0001 0000 | 0x0010 | 下行光纤断 |
| 0000 0000 0010 0000 | 0x0020 | 封锁 |
| 0000 0000 0100 0000 | 0x0040 | 旁路 |
| ... | ... | 其他状态保留 |

## 5. 实际应用示例

### 5.1 解析示例

以D3协议响应为例：
```
EB 90 01 01 D3 24 00 00 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 04 00 00 00 00 00 00 00 00 00 00 00 00 00 04 00 04 00 A4 AA AB
```

解析单元状态：
- 单元1：`04 00` = 0x0004 = 停止状态
- 单元2：`04 00` = 0x0004 = 停止状态
- 单元3：`04 00` = 0x0004 = 停止状态
- 单元4：`04 00` = 0x0004 = 停止状态
- 单元5：`04 00` = 0x0004 = 停止状态
- 单元6：`04 00` = 0x0004 = 停止状态
- 单元7：`04 00` = 0x0004 = 停止状态
- 单元8：`04 00` = 0x0004 = 停止状态
- 单元9：`04 00` = 0x0004 = 停止状态
- 单元10：`04 00` = 0x0004 = 停止状态
- 单元11-12：`00 00 00 00` = 旁路状态（未激活）

### 5.2 多状态示例

如果某单元同时处于停止和电源故障状态：
```
0C 00 = 0000 0000 0000 1100 = 0x000C = 停止(0x0004) + 电源故障(0x0008)
```

## 6. 注意事项和限制

1. 目前10kV设备一相最多支持12个单元，超出部分为保留区域。
2. 单元状态可能同时显示多个状态（如下行光纤断、封锁、停止、电源故障等）。
3. 状态码采用位运算方式，需要通过按位与(&)操作来判断特定状态是否存在。
4. 校验位的计算方法为：从地址字节开始到数据区结束的所有字节的累加和。
5. 在解析时需要注意数据的完整性和有效性，确保接收到的数据帧格式正确。
6. 当单元处于旁路状态时，通常不会显示其他状态信息。

## 7. 协议应用场景

本协议主要用于：

1. 监控各相单元的运行状态
2. 故障诊断和排查
3. 系统状态实时监控
4. 设备维护和预防性检修

通过定期查询单元状态，可以及时发现潜在问题，提高系统的可靠性和稳定性。