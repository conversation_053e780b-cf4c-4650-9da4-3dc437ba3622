#!/bin/bash

# 数据解析器服务启动脚本

# 获取脚本所在目录的父目录作为项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "启动数据解析器服务..."
echo "项目根目录: $PROJECT_ROOT"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查Python3是否安装
PYTHON3_CMD=""
if command -v python3 &> /dev/null; then
    PYTHON3_CMD="python3"
elif [ -f "/usr/bin/python3" ]; then
    PYTHON3_CMD="/usr/bin/python3"
else
    echo "错误: 未找到python3，请先安装Python3"
    exit 1
fi

echo "使用Python3: $PYTHON3_CMD"

# 检查并创建虚拟环境
VENV_DIR=".venv"
if [ ! -d "$VENV_DIR" ]; then
    echo "创建虚拟环境..."
    $PYTHON3_CMD -m venv "$VENV_DIR"
    if [ $? -ne 0 ]; then
        echo "错误: 虚拟环境创建失败"
        exit 1
    fi
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source "$VENV_DIR/bin/activate"
PYTHON3_CMD="$VENV_DIR/bin/python3"
PIP_CMD="$VENV_DIR/bin/pip"

# 检查是否安装了必要的依赖
echo "检查依赖包..."
$PYTHON3_CMD -c "import watchdog" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

# 创建data目录（如果不存在）
if [ ! -d "data" ]; then
    echo "创建data目录..."
    mkdir -p data
fi

# 启动服务
echo "启动数据解析器服务..."
$PYTHON3_CMD src/services/data_parser_service.py