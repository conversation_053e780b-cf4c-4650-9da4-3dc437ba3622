# 串口通信服务修改总结

## 修改概述

根据需求，对 `src/core/simple_main.py` 中的串口通信服务逻辑进行了重大重构，主要改进了59参数监控和修改机制，并解决了相对路径问题。

## 相对路径问题分析与解决

### 问题原因
1. **工作目录依赖性**: 原代码使用 `../../config/service.conf` 这样的相对路径，依赖于当前工作目录
2. **运行位置不一致**: 用户可能从项目根目录或 `src/core/` 目录运行代码，导致相对路径基准不同
3. **路径计算复杂**: 使用多层 `os.path.dirname()` 嵌套计算项目根目录，可读性差

### 解决方案
采用**基于代码文件位置的相对路径计算**：

```python
# 旧方案（有问题）
config_file = '../../config/service.conf'
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 新方案（正确）
current_file_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_file_dir, "..", "..")
project_root = os.path.normpath(project_root)
config_dir = os.path.join(project_root, "config")
config_file = os.path.join(config_dir, config_filename)
```

### 修复的文件路径
1. **配置文件路径**: `config/service.conf` 和 `config/service_windows.conf`
2. **数据目录路径**: `data/` 目录下的所有文件
3. **59参数文件**: `data/59_parsed_data.json`
4. **写入命令文件**: `data/write_cmd_data.json`
5. **MQTT配置文件**: `config/mqtt_config.json`

### 修复的文件列表
1. **`src/core/simple_main.py`**:
   - 修复 `SimpleDataRotator` 类的数据目录路径计算
   - 修复 `RealSerialService` 类的项目根目录路径计算
   - 修复 `load_config` 方法的配置文件路径计算

2. **`src/parsers/c59_data_parser.py`**:
   - 修复 `extract_startup_59_values` 函数的路径计算
   - 修复 `process_59_data_to_c0_format` 函数的路径计算

3. **`src/parsers/d6_data_parser.py`**:
   - 修复 `main` 函数的数据目录路径计算

4. **`src/parsers/d7_data_parser.py`**:
   - 修复 `main` 函数的数据目录路径计算

5. **`src/parsers/d8_data_parser.py`**:
   - 修复 `main` 函数的数据目录路径计算

6. **`src/services/mqtt_publisher.py`**:
   - 修复 `MQTTConfigManager` 类的配置文件路径计算
   - 修复 `MQTT59Publisher` 类的配置文件路径计算
   - 添加 `os` 模块导入

### 验证结果
- ✅ 从项目根目录运行正常
- ✅ 从 `src/core/` 目录运行正常
- ✅ 从 `src/services/` 目录运行正常
- ✅ 从 `src/parsers/` 目录运行正常
- ✅ 从项目外目录（如`/tmp/`）运行正常
- ✅ 配置文件加载成功
- ✅ 数据文件路径正确
- ✅ MQTT配置加载成功
- ✅ 所有解析器路径正确
- ✅ 串口服务路径正确

### 部署便利性提升
通过本次修复，项目现在具备了更好的部署便利性：
- ✨ **可移植性**: 不再依赖绝对路径，可以在任意目录部署
- ✨ **多环境兼容**: 在开发、测试、生产环境下都可正常运行
- ✨ **运行灵活性**: 可以从任意工作目录启动服务
- ✨ **容器化友好**: 适合 Docker 等容器化部署
- ✨ **维护简化**: 附属文件和配置可以随项目一起移动

## 主要修改内容

### 1. 修改循环序列逻辑
- **原逻辑**: 在主循环中单独检查59参数文件时间戳变化
- **新逻辑**: 在每个循环指令结束后，直接检查 `59_parsed_data.json` 的 `value` 和 `writeValue` 差异
- **优势**: 实时性更强，不依赖文件修改时间戳

### 2. 移除基于时间戳的检测机制
- 移除了 `last_59_file_mtime` 和 `last_59_data` 变量
- 移除了 `_initialize_59_baseline()` 方法
- 移除了 `check_59_param_changes()` 方法中的时间戳检测逻辑

### 3. 实现新的参数差异检测
- 新增 `check_59_param_changes_directly()` 方法
- 直接遍历 `59_parsed_data.json` 中的所有参数
- 比较每个参数的 `value` 和 `writeValue` 字段
- 当值不同时，自动生成57修改指令队列

### 4. 完整的修改流程实现
实现了完整的参数修改确认流程：
1. 检测到参数差异后生成57修改指令
2. 执行所有57指令（收到55响应）
3. 发送37修改完成确认指令（收到5B响应）
4. 发送59查询指令，验证参数修改结果
5. 接收259帧响应数据，更新 `realtime_data.json`

### 5. 修改59指令响应接收逻辑
- **原逻辑**: 等待固定的4秒时间接收数据
- **新逻辑**: 精确匹配259帧响应数，接收完毕后立即停止
- **安全机制**:
  - 添加10秒最大超时保护
  - 增加 `is_receiving_259_frames` 状态标志
  - 接收期间不允许插入其他指令

### 6. 状态管理优化
- 新增 `pending_259_frames` 和 `is_receiving_259_frames` 状态变量
- 移除不再需要的 `param_59_frames_received` 和 `param_59_total_frames` 变量
- 优化了启动序列和循环序列的状态管理

## 关键代码变更

### 新增方法
```python
def check_59_param_changes_directly(self):
    """直接检查59参数数据文件，对比writeValue和value差异并生成57修改指令"""
```

### 修改的方法
```python
async def _execute_loop_sequence(self):
    """修改为在每个循环指令结束后直接检查59参数差异"""

async def _receive_59_responses(self, command: str):
    """从等待4秒改为匹配259帧响应数"""

async def _check_and_execute_57_commands(self):
    """移除文件检查逻辑，仅处理队列中的57指令"""
```

## 测试验证

创建了 `test_modified_logic.py` 测试脚本，验证了：
1. ✅ 59参数差异检测逻辑正确
2. ✅ 57指令构建功能正常
3. ✅ 参数修改队列生成准确

测试结果显示所有核心功能正常工作。

## 运行流程

新的运行流程如下：

1. **启动序列**: `['C0', '59', 'C5', 'D6', 'D7', 'D8']`
2. **循环序列**: `['C1', 'C4', 'D0', 'D1', 'D2', 'C4', 'D3', 'D4', 'D5', 'C4', 'C0']`
3. **每个循环指令执行后**:
   - 检查 `59_parsed_data.json` 中 `value` vs `writeValue`
   - 如有差异，生成57修改指令队列
4. **参数修改流程**:
   - 执行所有57指令 → 收到55响应
   - 发送37指令 → 收到5B响应
   - 发送59查询 → 接收259帧数据
   - 更新 `realtime_data.json`

## 优势总结

1. **实时性**: 循环指令执行后立即检查参数差异
2. **准确性**: 直接比较数值，不依赖文件时间戳
3. **可靠性**: 精确的259帧匹配，避免数据丢失
4. **完整性**: 实现了完整的修改确认流程
5. **安全性**: 接收期间禁止其他指令插入

## 兼容性

- 保持了与现有数据格式的完全兼容
- 不影响其他命令的正常执行
- 向后兼容现有的配置文件和数据结构

修改已完成并通过测试验证，可以正常投入使用。