#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正后的C5版本信息解析功能测试脚本
验证第12组复合版本信息的正确解析规则
"""

def bcd_to_decimal(bcd_byte):
    """
    将BCD码字节转换为十进制数
    
    Args:
        bcd_byte: BCD码字节
        
    Returns:
        int: 十进制数
    """
    return (bcd_byte >> 4) * 10 + (bcd_byte & 0x0F)

def parse_standard_group(group_data):
    """
    解析标准4字节组数据
    
    Args:
        group_data: 4字节组数据
        
    Returns:
        dict: 解析结果
    """
    # 按实际字节顺序解析：第1个字节是版本号，后3个字节是日期
    version_byte = group_data[0]  # 第1个字节是版本号
    date_bytes = group_data[1:4]  # 后3个字节是日期
    
    # 版本号解析：十六进制转十进制显示
    if version_byte == 0x88:
        version_str = "8.8"
    else:
        major = version_byte >> 4  # 高4位
        minor = version_byte & 0x0F  # 低4位
        version_str = f"{major}.{minor}"
    
    # 日期解析：BCD码格式，YY-MM-DD
    if all(b == 0x88 for b in date_bytes):
        date_str = "88-88-88"
    elif all(b == 0x00 for b in date_bytes):
        date_str = "00-00-00"
    else:
        # BCD码转换
        yy = bcd_to_decimal(date_bytes[0])  # 年份
        mm = bcd_to_decimal(date_bytes[1])  # 月份
        dd = bcd_to_decimal(date_bytes[2])  # 日期
        date_str = f"{yy:02d}-{mm:02d}-{dd:02d}"
    
    return {
        'raw_bytes': ' '.join(f"{b:02X}" for b in group_data),
        'version': version_str,
        'date': date_str
    }

def parse_group_12_composite(group_data):
    """
    解析第12组复合版本信息（修正后的规则）
    
    Args:
        group_data: 4字节组数据 (01 00 00 01)
        
    Returns:
        dict: 包含两个子模块的解析结果
    """
    result = {}
    
    # 按照修正后的解析规则：
    # 字节1 (01): 跳过处理
    # 字节2 (00): 主控CPU板IO板版本号
    # 字节3-4 (00 01): 主控CPU板DSP子版本号
    
    print(f"第12组原始数据: {' '.join(f'{b:02X}' for b in group_data)}")
    print(f"字节1 (跳过): {group_data[0]:02X}")
    print(f"字节2 (IO板): {group_data[1]:02X}")
    print(f"字节3-4 (DSP子): {group_data[2]:02X} {group_data[3]:02X}")
    
    # 子模块A：主控CPU板IO板版本号 (字节2: 00)
    io_version_byte = group_data[1]  # 第2个字节: 00
    io_major = (io_version_byte >> 4) & 0x0F  # 高4位
    io_minor = io_version_byte & 0x0F  # 低4位
    result["主控CPU板IO板版本号"] = {
        'raw_bytes': f"{io_version_byte:02X}",
        'version': f"{io_major}.{io_minor}",  # 0.0
        'date': ""  # 第12组不包含日期信息
    }
    
    # 子模块B：主控CPU板DSP子版本号 (字节3-4: 00 01)
    dsp_version_bytes = group_data[2:4]  # 00 01
    dsp_major = bcd_to_decimal(dsp_version_bytes[0])  # 00 → 0
    dsp_minor = bcd_to_decimal(dsp_version_bytes[1])  # 01 → 1
    result["主控CPU板DSP子版本号"] = {
        'raw_bytes': ' '.join(f"{b:02X}" for b in dsp_version_bytes),
        'version': f"{dsp_major:02d}.{dsp_minor:02d}",  # 00.01
        'date': ""  # 第12组不包含日期信息
    }
    
    return result

def parse_version_info(data):
    """
    解析C5版本信息数据，使用修正后的解析规则
    
    Args:
        data: 48字节的版本信息数据
        
    Returns:
        dict: 解析后的版本信息
    """
    if len(data) != 48:
        raise ValueError("数据长度必须为48字节")
    
    # 定义需要解析的模块映射（只解析指定的模块）
    module_mapping = {
        1: "主控CPU板DSP版本号",
        2: "主控CPU板CPLD版本号", 
        3: "主控A相PWM板DSP版本号",
        4: "主控A相PWM板FPGA版本号",
        5: "主控B相PWM板DSP版本号",
        6: "主控B相PWM板FPGA版本号",
        7: "主控C相PWM板DSP版本号",
        8: "主控C相PWM板FPGA版本号",
        # 组9-11跳过解析
        12: "复合版本信息"  # 特殊处理
    }
    
    result = {}
    
    # 逐组解析，每组4字节
    for i in range(12):
        group_index = i + 1
        start_idx = i * 4
        group_data = data[start_idx:start_idx + 4]
        
        # 跳过未定义的模块
        if group_index not in module_mapping:
            continue
            
        if group_index == 12:
            # 第12组特殊处理：复合版本信息
            result.update(parse_group_12_composite(group_data))
        else:
            # 标准解析（组1-8）
            module_name = module_mapping[group_index]
            parsed_info = parse_standard_group(group_data)
            result[module_name] = parsed_info
    
    return result

def test_corrected_c5_parsing():
    """测试修正后的C5版本信息解析功能"""
    print("测试修正后的C5版本信息解析功能")
    print("="*80)
    
    # 使用样例数据
    sample_data = bytes([
        0x83, 0x15, 0x09, 0x23,  # 组1: 主控CPU板DSP版本号
        0x20, 0x25, 0x03, 0x06,  # 组2: 主控CPU板CPLD版本号
        0x88, 0x88, 0x88, 0x88,  # 组3: 主控A相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组4: 主控A相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组5: 主控B相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组6: 主控B相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组7: 主控C相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组8: 主控C相PWM板FPGA版本号
        0x00, 0x00, 0x00, 0x00,  # 组9: 未定义模块（跳过）
        0x00, 0x00, 0x00, 0x00,  # 组10: 未定义模块（跳过）
        0x00, 0x00, 0x00, 0x00,  # 组11: 未定义模块（跳过）
        0x01, 0x00, 0x00, 0x01   # 组12: 复合版本信息
    ])
    
    print(f"测试数据长度: {len(sample_data)} 字节")
    print(f"测试数据: {sample_data.hex().upper()}")
    print()
    
    try:
        parsed_info = parse_version_info(sample_data)
        
        print("\n解析结果:")
        print("-" * 80)
        print(f"{'模块名称':<30} {'版本号':<10} {'修改日期':<12} {'原始字节'}")
        print("-" * 80)
        
        for module_name, info in parsed_info.items():
            date_display = info['date'] if info['date'] else "(无日期)"
            print(f"{module_name:<30} {info['version']:<10} {date_display:<12} {info['raw_bytes']}")
        
        print("\n验证修正后的预期结果:")
        print("-" * 50)
        
        # 验证修正后的预期结果
        expected_results = {
            "主控CPU板IO板版本号": {"version": "0.0", "date": ""},
            "主控CPU板DSP子版本号": {"version": "00.01", "date": ""}
        }
        
        print("重点验证第12组复合版本信息:")
        all_correct = True
        for module_name, expected in expected_results.items():
            if module_name in parsed_info:
                actual = parsed_info[module_name]
                version_match = actual['version'] == expected['version']
                date_match = actual['date'] == expected['date']
                
                if version_match and date_match:
                    print(f"✓ {module_name}: 版本{actual['version']}, 日期{actual['date'] or '(无)'} - 正确")
                else:
                    print(f"✗ {module_name}: 期望版本{expected['version']}/日期{expected['date'] or '(无)'}, "
                          f"实际版本{actual['version']}/日期{actual['date'] or '(无)'} - 错误")
                    all_correct = False
            else:
                print(f"✗ {module_name}: 未找到解析结果")
                all_correct = False
        
        if all_correct:
            print("\n✓ 第12组复合版本信息解析正确！")
        else:
            print("\n✗ 第12组复合版本信息解析不正确")
            
        print(f"\n解析的模块总数: {len(parsed_info)}")
        
    except Exception as e:
        print(f"解析错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "="*80)
    print("测试完成")

if __name__ == "__main__":
    test_corrected_c5_parsing()
